<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ملخص مشروع منصة خدمات ديناميت</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Dynamite Orange & Warm Neutrals -->
    <!-- Application Structure Plan: تم تصميم التطبيق كهيكل قائم على التبويبات (نظرة عامة، الهيكل والتصميم، خريطة الطريق) لتسهيل التنقل. هذا النهج يقسم وثيقة المشروع المعقدة إلى أقسام منطقية يمكن للمستخدم استكشافها بسهولة. "نظرة عامة" تقدم ملخصًا سريعًا. "الهيكل والتصميم" يتعمق في التفاصيل الفنية مثل أدوار المستخدمين وتصميمات الصفحات عبر قوائم تفاعلية. "خريطة الطريق" تحول الجدول الزمني إلى مخطط مرئي. هذا التصميم يعزز الفهم ويجعل المعلومات أكثر قابلية للاستهلاك مقارنة بقراءة وثيقة طويلة. -->
    <!-- Visualization & Content Choices: 
        - نظرة عامة: استخدام بطاقات (HTML/Tailwind) للميزات الرئيسية ومكدس التكنولوجيا لتقديم المعلومات بشكل مرئي وجذاب.
        - أدوار المستخدمين: عرض الأدوار والصلاحيات عبر نظام تبويب تفاعلي (HTML/JS) لتمكين المقارنة السهلة بين الأدوار بدلاً من قائمة نصية طويلة.
        - تصميم الصفحات: استخدام قوائم منسدلة (Accordion) لعرض تفاصيل كل صفحة، مما يقلل الفوضى البصرية ويسمح للمستخدم بالتركيز على قسم واحد في كل مرة.
        - خريطة الطريق: تحويل مراحل المشروع إلى مخطط زمني أفقي (HTML/Tailwind) لتوضيح التسلسل والمدة الزمنية لكل مرحلة بشكل فعال.
        - الرسوم البيانية: إضافة أمثلة لرسوم بيانية (Chart.js) لمحاكاة لوحة التحكم النهائية، مما يجعل مفهوم المنتج النهائي أكثر واقعية وملموسًا.
    -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8fafc;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .nav-link {
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .nav-link.active {
            border-color: #FF4500;
            color: #FF4500;
            font-weight: 700;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">
    <div class="container mx-auto p-4 sm:p-6 lg:p-8">

        <header class="text-center mb-8">
            <h1 class="text-4xl font-bold text-slate-900 mb-2">ملخص مشروع منصة خدمات ديناميت 🧨</h1>
            <p class="text-lg text-slate-600">نظرة تفاعلية على وثيقة تطوير المشروع</p>
        </header>

        <nav class="bg-white rounded-lg shadow-sm mb-8 sticky top-4 z-10">
            <ul class="flex items-center justify-center p-2 gap-x-4 md:gap-x-8">
                <li><a href="#overview" class="nav-link active p-2 text-lg" data-tab="overview">نظرة عامة</a></li>
                <li><a href="#architecture" class="nav-link p-2 text-lg" data-tab="architecture">الهيكل والتصميم</a></li>
                <li><a href="#roadmap" class="nav-link p-2 text-lg" data-tab="roadmap">خريطة الطريق</a></li>
            </ul>
        </nav>

        <main>
            <!-- Overview Section -->
            <section id="overview" class="tab-content active">
                <div class="bg-white p-6 rounded-lg shadow-md mb-8">
                    <h2 class="text-2xl font-bold mb-4 text-[#FF4500]">مفهوم المشروع</h2>
                    <p class="text-slate-700 leading-relaxed">
                        منصة خدمات ديناميت هي سوق متعدد الخدمات يهدف إلى ربط المستخدمين بمقدمي الخدمات في فئات متنوعة مثل الخدمات المنزلية، النقل، الصحة، والتعليم. ستمكن المنصة المستخدمين من تصفح الخدمات عبر خرائط تفاعلية، التواصل الفوري، تلقي الإشعارات، وإدارة حجوزاتهم بسلاسة عبر واجهات الويب وتطبيق الموبايل.
                    </p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-3">🗺️ دمج الخرائط</h3>
                        <p>بحث وعرض مقدمي الخدمات حسب الموقع الجغرافي باستخدام خرائط تفاعلية.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-3">💬 رسائل فورية</h3>
                        <p>دردشة في الوقت الفعلي بين المستخدمين ومقدمي الخدمات لدعم التواصل المباشر.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-3">🔔 نظام الإشعارات</h3>
                        <p>إشعارات فورية عبر الموبايل والويب لتحديثات الحجوزات والرسائل الجديدة.</p>
                    </div>
                     <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-3">⚙️ إدارة متكاملة</h3>
                        <p>لوحات تحكم قوية للمستخدمين والمزودين والإداريين لإدارة كافة جوانب المنصة.</p>
                    </div>
                     <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-3">👥 أدوار وصلاحيات</h3>
                        <p>نظام تحكم دقيق (RBAC) لضمان الأمان وتخصيص الوصول لكل فئة مستخدم.</p>
                    </div>
                     <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-3">📱 تطبيق متعدد المنصات</h3>
                        <p>تجربة مستخدم متكاملة عبر الويب وتطبيقات الموبايل (iOS/Android) باستخدام Flutter.</p>
                    </div>
                </div>
                
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-2xl font-bold mb-4 text-[#FF4500]">التقنيات المستخدمة</h2>
                     <div class="flex flex-wrap items-center justify-center gap-6 text-center">
                        <div class="p-4 bg-slate-100 rounded-lg w-28"><span class="text-3xl">🐘</span><p class="font-semibold mt-2">Laravel (PHP)</p></div>
                        <div class="p-4 bg-slate-100 rounded-lg w-28"><span class="text-3xl">🎨</span><p class="font-semibold mt-2">Vue.js</p></div>
                        <div class="p-4 bg-slate-100 rounded-lg w-28"><span class="text-3xl">📱</span><p class="font-semibold mt-2">Flutter</p></div>
                        <div class="p-4 bg-slate-100 rounded-lg w-28"><span class="text-3xl">🐬</span><p class="font-semibold mt-2">MySQL</p></div>
                        <div class="p-4 bg-slate-100 rounded-lg w-28"><span class="text-3xl">🔥</span><p class="font-semibold mt-2">Firebase</p></div>
                        <div class="p-4 bg-slate-100 rounded-lg w-28"><span class="text-3xl">☁️</span><p class="font-semibold mt-2">AWS S3</p></div>
                    </div>
                </div>
            </section>

            <!-- Architecture Section -->
            <section id="architecture" class="tab-content">
                <div class="bg-white p-6 rounded-lg shadow-md mb-8">
                    <h2 class="text-2xl font-bold mb-4 text-[#FF4500]">أدوار المستخدمين والصلاحيات (RBAC)</h2>
                    <p class="mb-6">تعتمد المنصة على نظام التحكم في الوصول القائم على الأدوار لضمان الأمان والخصوصية. انقر على كل دور لعرض صلاحياته الرئيسية.</p>
                    <div id="roles-container" class="flex flex-wrap gap-2 mb-4 border-b pb-4"></div>
                    <div id="permissions-display" class="bg-slate-100 p-4 rounded-lg min-h-[150px]"></div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-md">
                     <h2 class="text-2xl font-bold mb-4 text-[#FF4500]">تصميم الصفحات والشاشات</h2>
                     <p class="mb-6">نظرة عامة على تصميمات الواجهات الرئيسية للمنصة. انقر على كل قسم لتوسيع وعرض تفاصيله.</p>
                     <div id="pages-accordion" class="space-y-4"></div>
                </div>
            </section>
            
            <!-- Roadmap Section -->
            <section id="roadmap" class="tab-content">
                 <div class="bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-2xl font-bold mb-6 text-[#FF4500]">خريطة طريق المشروع</h2>
                    <p class="mb-8 text-center">تهدف الخطة إلى إطلاق نسخة أولية (MVP) في غضون 3 أشهر، مقسمة على المراحل التالية:</p>
                    <div class="relative w-full">
                        <!-- Timeline line -->
                        <div class="absolute left-1/2 h-full w-0.5 bg-slate-300 transform -translate-x-1/2"></div>
                        <div class="space-y-12">
                            <div class="flex items-center w-full">
                                <div class="w-1/2 pr-8 text-right">
                                    <h3 class="font-bold text-lg">التخطيط والتصميم</h3>
                                    <p class="text-slate-600">رسم الإطارات، مخطط قاعدة البيانات، توثيق API.</p>
                                </div>
                                <div class="relative w-12 h-12 bg-[#FF4500] rounded-full flex items-center justify-center text-white font-bold z-10 text-sm">أسبوعان</div>
                                <div class="w-1/2"></div>
                            </div>
                            <div class="flex items-center w-full">
                                <div class="w-1/2"></div>
                                <div class="relative w-12 h-12 bg-[#FF4500] rounded-full flex items-center justify-center text-white font-bold z-10 text-sm">4-6 أسابيع</div>
                                <div class="w-1/2 pl-8 text-left">
                                    <h3 class="font-bold text-lg">تطوير الباك-إند</h3>
                                    <p class="text-slate-600">بناء نقاط النهاية للـAPI، المصادقة، ونظام الأدوار.</p>
                                </div>
                            </div>
                            <div class="flex items-center w-full">
                                <div class="w-1/2 pr-8 text-right">
                                    <h3 class="font-bold text-lg">تطوير الواجهات</h3>
                                    <p class="text-slate-600">بناء لوحات التحكم (Vue.js) وتطبيق الموبايل (Flutter).</p>
                                </div>
                                <div class="relative w-12 h-12 bg-[#FF4500] rounded-full flex items-center justify-center text-white font-bold z-10 text-sm">4-6 أسابيع</div>
                                <div class="w-1/2"></div>
                            </div>
                            <div class="flex items-center w-full">
                                <div class="w-1/2"></div>
                                <div class="relative w-12 h-12 bg-[#FF4500] rounded-full flex items-center justify-center text-white font-bold z-10 text-sm">2-4 أسابيع</div>
                                <div class="w-1/2 pl-8 text-left">
                                    <h3 class="font-bold text-lg">التكامل والاختبار</h3>
                                    <p class="text-slate-600">اختبار شامل للمنصة وتدقيق الأمان.</p>
                                </div>
                            </div>
                            <div class="flex items-center w-full">
                                <div class="w-1/2 pr-8 text-right">
                                    <h3 class="font-bold text-lg">النشر والإطلاق</h3>
                                    <p class="text-slate-600">النشر على السيرفرات والإطلاق الرسمي.</p>
                                </div>
                                <div class="relative w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white font-bold z-10 text-sm">أسبوع</div>
                                <div class="w-1/2"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md mt-8">
                    <h2 class="text-2xl font-bold mb-4 text-[#FF4500]">تصور لوحة التحكم الإدارية</h2>
                    <p class="mb-6">سيحتوي المنتج النهائي على لوحة تحكم قوية مع تحليلات في الوقت الفعلي. هذه أمثلة على الرسوم البيانية التي سيتم عرضها:</p>
                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="chart-container"><canvas id="userGrowthChart"></canvas></div>
                        <div class="chart-container"><canvas id="revenueChart"></canvas></div>
                    </div>
                </div>
            </section>
        </main>
        
        <footer class="text-center mt-12 text-slate-500 text-sm">
            <p>تم إنشاء هذا الملخص التفاعلي من وثيقة مشروع ديناميت بتاريخ 14 سبتمبر 2025.</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const navLinks = document.querySelectorAll('.nav-link');
            const tabContents = document.querySelectorAll('.tab-content');

            const setActiveTab = (tabId) => {
                navLinks.forEach(link => {
                    link.classList.toggle('active', link.dataset.tab === tabId);
                });
                tabContents.forEach(content => {
                    content.classList.toggle('active', content.id === tabId);
                });
                window.location.hash = tabId;
            };

            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    setActiveTab(e.target.dataset.tab);
                });
            });

            const initialTab = window.location.hash.substring(1) || 'overview';
            setActiveTab(initialTab);

            // Roles and Permissions data
            const rolesData = {
                'السوبر أدمن': ['وصول كامل لكل شيء', 'إدارة المستخدمين والخدمات', 'تعديل إعدادات النظام'],
                'الأدمن': ['إدارة المستخدمين والخدمات', 'مراقبة الحجوزات', 'إرسال إشعارات عامة'],
                'مدير الخدمة': ['الإشراف على المزودين في فئته', 'الموافقة على القوائم', 'حل النزاعات'],
                'المزود': ['إدارة خدماته وتوافره', 'التواصل مع العملاء', 'متابعة حجوزاته'],
                'العميل': ['تصفح وحجز الخدمات', 'التواصل مع المزودين', 'تقييم الخدمات'],
                'الضيف': ['وصول محدود للعرض فقط', 'تصفح الخدمات المتاحة']
            };

            const rolesContainer = document.getElementById('roles-container');
            const permissionsDisplay = document.getElementById('permissions-display');
            let firstRole = true;

            for (const role in rolesData) {
                const button = document.createElement('button');
                button.textContent = role;
                button.className = 'px-4 py-2 rounded-lg transition duration-200';
                if (firstRole) {
                    button.classList.add('bg-[#FF4500]', 'text-white');
                } else {
                    button.classList.add('bg-slate-200', 'text-slate-700', 'hover:bg-slate-300');
                }
                button.addEventListener('click', () => {
                    document.querySelectorAll('#roles-container button').forEach(btn => {
                        btn.classList.remove('bg-[#FF4500]', 'text-white');
                        btn.classList.add('bg-slate-200', 'text-slate-700', 'hover:bg-slate-300');
                    });
                    button.classList.add('bg-[#FF4500]', 'text-white');
                    button.classList.remove('bg-slate-200', 'text-slate-700', 'hover:bg-slate-300');
                    
                    permissionsDisplay.innerHTML = `<h3 class="font-bold text-lg mb-2">${role}</h3><ul class="list-disc pr-5 space-y-1">${rolesData[role].map(p => `<li>${p}</li>`).join('')}</ul>`;
                });
                rolesContainer.appendChild(button);
                if(firstRole) {
                    button.click();
                    firstRole = false;
                }
            }
            
            // Pages Accordion data
            const pagesData = {
                "لوحة التحكم الإدارية (Vue.js)": {
                    icon: '👑',
                    pages: [
                        { name: 'لوحة التحكم الرئيسية', details: 'عرض المقاييس الرئيسية، الرسوم البيانية، والإجراءات السريعة.' },
                        { name: 'إدارة المستخدمين', details: 'جدول بجميع المستخدمين مع خيارات البحث، التصفية، التحرير، والحظر.' },
                        { name: 'إدارة الخدمات', details: 'إدارة فئات الخدمات، تعيين المديرين، وتحديد الصلاحيات الخاصة بكل خدمة.' },
                        { name: 'إدارة الحجوزات', details: 'عرض جميع الحجوزات مع إمكانية التصفية حسب الحالة وعرض التفاصيل.' },
                        { name: 'التحليلات والتقارير', details: 'إنشاء تقارير مخصصة حول أداء المنصة وتصديرها.' }
                    ]
                },
                "لوحة المستخدم (Vue.js)": {
                    icon: '👤',
                    pages: [
                        { name: 'الملف الشخصي', details: 'تحرير البيانات الشخصية، إدارة طرق الدفع، وصورة الملف الشخصي.' },
                        { name: 'تصفح الخدمات', details: 'واجهة الخريطة وقائمة الخدمات مع فلاتر للبحث والتصنيف.' },
                        { name: 'الحجوزات', details: 'قائمة بالحجوزات القادمة والسابقة مع روابط للدردشة والتفاصيل.' },
                        { name: 'صندوق الدردشة', details: 'محادثات فورية مع الأطراف الأخرى.' },
                        { name: 'إدارة التوافر (للمزودين)', details: 'تقويم لتحديد أوقات العمل وتفعيل/تعطيل التوافر.' }
                    ]
                },
                "تطبيق الموبايل (Flutter)": {
                    icon: '📱',
                    pages: [
                        { name: 'الشاشة الرئيسية', details: 'عرض الخريطة كواجهة رئيسية مع شريط سفلي للتنقل السريع.' },
                        { name: 'تفاصيل الخدمة', details: 'عرض وصف الخدمة، التقييمات، الصور، وزر الحجز الفوري.' },
                        { name: 'شاشة الحجوزات', details: 'قائمة مبوبة للحجوزات القادمة والتاريخية.' },
                        { name: 'شاشة الدردشة', details: 'واجهة محادثة بسيطة ومحسنة للموبايل.' },
                        { name: 'الملف الشخصي', details: 'إدارة الحساب والإعدادات الشخصية بسهولة.' }
                    ]
                }
            };
            
            const accordionContainer = document.getElementById('pages-accordion');
            for(const category in pagesData) {
                const item = pagesData[category];
                const accordionItem = document.createElement('div');
                accordionItem.className = 'border border-slate-200 rounded-lg';
                
                const button = document.createElement('button');
                button.className = 'w-full text-right p-4 flex justify-between items-center bg-slate-100 hover:bg-slate-200 transition';
                button.innerHTML = `
                    <span class="font-bold text-lg">${item.icon} ${category}</span>
                    <span class="transform transition-transform duration-300">▼</span>
                `;
                
                const content = document.createElement('div');
                content.className = 'accordion-content p-4 border-t border-slate-200';
                
                let pagesList = '<ul class="space-y-3">';
                item.pages.forEach(page => {
                    pagesList += `<li><strong class="font-semibold">${page.name}:</strong> ${page.details}</li>`;
                });
                pagesList += '</ul>';
                content.innerHTML = pagesList;
                
                button.addEventListener('click', () => {
                    const icon = button.querySelector('span:last-child');
                    if (content.style.maxHeight) {
                        content.style.maxHeight = null;
                        icon.style.transform = 'rotate(0deg)';
                    } else {
                        document.querySelectorAll('.accordion-content').forEach(el => el.style.maxHeight = null);
                        document.querySelectorAll('#pages-accordion button span:last-child').forEach(el => el.style.transform = 'rotate(0deg)');
                        content.style.maxHeight = content.scrollHeight + "px";
                        icon.style.transform = 'rotate(180deg)';
                    }
                });
                
                accordionItem.appendChild(button);
                accordionItem.appendChild(content);
                accordionContainer.appendChild(accordionItem);
            }
            
            // Chart.js instances
            Chart.defaults.font.family = "'Tajawal', sans-serif";
            
            const userGrowthCtx = document.getElementById('userGrowthChart')?.getContext('2d');
            if (userGrowthCtx) {
                new Chart(userGrowthCtx, {
                    type: 'line',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'نمو المستخدمين',
                            data: [120, 190, 300, 500, 820, 1230],
                            borderColor: '#FF4500',
                            backgroundColor: 'rgba(255, 69, 0, 0.1)',
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false }, title: { display: true, text: 'نمو المستخدمين الشهري' } }
                    }
                });
            }

            const revenueCtx = document.getElementById('revenueChart')?.getContext('2d');
            if (revenueCtx) {
                new Chart(revenueCtx, {
                    type: 'bar',
                    data: {
                        labels: ['سباكة', 'تدريس', 'صحة', 'نقل', 'خدمات منزلية'],
                        datasets: [{
                            label: 'الإيرادات',
                            data: [5200, 7600, 4800, 6100, 9300],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.6)',
                                'rgba(54, 162, 235, 0.6)',
                                'rgba(255, 206, 86, 0.6)',
                                'rgba(75, 192, 192, 0.6)',
                                'rgba(153, 102, 255, 0.6)'
                            ]
                        }]
                    },
                     options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false }, title: { display: true, text: 'الإيرادات حسب فئة الخدمة' } },
                        indexAxis: 'y',
                    }
                });
            }
        });
    </script>
</body>
</html>
