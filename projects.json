{"success": true, "projects": [{"id": "psychai", "title": {"ar": "PsychAI", "en": "PsychAI"}, "description": {"ar": "مشروع Laravel للذكاء الاصطناعي النفسي - نظام تحليل نفسي متقدم باستخدام تقنيات الذكاء الاصطناعي", "en": "Laravel project for psychological AI - Advanced psychological analysis system using artificial intelligence techniques"}, "detailedDescription": {"ar": "نظام متطور للتحليل النفسي باستخدام تقنيات الذكاء الاصطناعي المتقدمة. يوفر النظام تحليلاً شاملاً للحالة النفسية للمستخدمين من خلال استبيانات ذكية وخوارزميات تعلم الآلة. يتضمن النظام واجهة مستخدم سهلة الاستخدام ولوحة تحكم شاملة للمختصين النفسيين.", "en": "Advanced psychological analysis system using cutting-edge artificial intelligence techniques. The system provides comprehensive psychological state analysis for users through intelligent questionnaires and machine learning algorithms. It includes a user-friendly interface and comprehensive dashboard for psychological specialists."}, "url": "/PsychAI/", "icon": "🧠", "status": "active", "technologies": ["<PERSON><PERSON>", "PHP", "MySQL", "JavaScript", "Bootstrap", "AI/ML", "Python"], "startDate": "2024-01-15", "lastUpdated": "2024-12-15", "features": {"ar": ["تحليل نفسي ذكي باستخدام الذكاء الاصطناعي", "استبيانات تفاعلية متقدمة", "لوحة تحكم شاملة للمختصين", "تقارير مفصلة وإحصائيات", "نظام إدارة المرضى", "واجهة مستخدم متجاوبة"], "en": ["Intelligent psychological analysis using AI", "Advanced interactive questionnaires", "Comprehensive dashboard for specialists", "Detailed reports and statistics", "Patient management system", "Responsive user interface"]}}, {"id": "schoolproject11", "title": {"ar": "SchoolProject11", "en": "SchoolProject11"}, "description": {"ar": "نظام إدارة مدرسية متكامل - منصة تعليمية شاملة لإدارة الطلاب والمواد الدراسية مع نظام محادثات تفاعلي وإدارة الأدوار والصلاحيات", "en": "Integrated school management system - Comprehensive educational platform for managing students and subjects with interactive chat system and role-based permissions"}, "detailedDescription": {"ar": "منصة تعليمية متكاملة تهدف إلى تسهيل إدارة العملية التعليمية في المدارس. يوفر النظام أدوات شاملة لإدارة الطلاب والمعلمين والمواد الدراسية، بالإضافة إلى نظام محادثات تفاعلي يسمح بالتواصل الفعال بين جميع أطراف العملية التعليمية. يتضمن النظام إدارة متقدمة للأدوار والصلاحيات لضمان الأمان والخصوصية.", "en": "Integrated educational platform aimed at facilitating school management processes. The system provides comprehensive tools for managing students, teachers, and subjects, along with an interactive chat system that enables effective communication between all educational stakeholders. The system includes advanced role and permission management to ensure security and privacy."}, "url": "/SchoolProject11/", "icon": "🏫", "status": "active", "technologies": ["<PERSON><PERSON>", "PHP", "MySQL", "JavaScript", "Vue.js", "Bootstrap", "WebSocket"], "startDate": "2024-03-10", "lastUpdated": "2024-12-10", "features": {"ar": ["إدارة شاملة للطلاب والمعلمين", "نظام محادثات تفاعلي في الوقت الفعلي", "إدارة المواد الدراسية والجداول", "نظام الدرجات والتقييمات", "إدارة الأدوار والصلاحيات", "تقارير وإحصائيات مفصلة", "واجهة متجاوبة لجميع الأجهزة"], "en": ["Comprehensive student and teacher management", "Real-time interactive chat system", "Subject and schedule management", "Grading and assessment system", "Role and permission management", "Detailed reports and statistics", "Responsive interface for all devices"]}}, {"id": "dynamitego", "title": {"ar": "DynamiteGo", "en": "DynamiteGo"}, "description": {"ar": "نظام إدارة مدرسية متكامل - منصة تعليمية شاملة لإدارة الطلاب والمواد الدراسية مع نظام محادثات تفاعلي وإدارة الأدوار والصلاحيات", "en": "Integrated school management system - Comprehensive educational platform for managing students and subjects with interactive chat system and role-based permissions"}, "detailedDescription": {"ar": "منصة تعليمية حديثة ومتطورة تركز على توفير تجربة تعليمية متميزة. يتميز النظام بواجهة مستخدم عصرية وأدوات إدارية متقدمة تسهل على المدارس إدارة عملياتها اليومية. يشمل النظام إدارة الطلاب والمعلمين والمناهج الدراسية، بالإضافة إلى نظام تقييم شامل ونظام إشعارات ذكي.", "en": "Modern and advanced educational platform focused on providing an exceptional learning experience. The system features a contemporary user interface and advanced administrative tools that make it easy for schools to manage their daily operations. The system includes student, teacher, and curriculum management, along with a comprehensive assessment system and smart notification system."}, "url": "/DynamiteGo/", "icon": "🚀", "status": "active", "technologies": ["<PERSON><PERSON>", "PHP", "MySQL", "JavaScript", "React", "Tailwind CSS", "Redis"], "startDate": "2024-05-20", "lastUpdated": "2024-12-08", "features": {"ar": ["واجهة مستخدم عصرية ومتجاوبة", "إدارة متقدمة للطلاب والمعلمين", "نظام تقييم شامل ومرن", "إدارة المناهج والمواد الدراسية", "نظام إشعارات ذكي", "تقارير تحليلية متقدمة", "دعم متعدد اللغات"], "en": ["Modern and responsive user interface", "Advanced student and teacher management", "Comprehensive and flexible assessment system", "Curriculum and subject management", "Smart notification system", "Advanced analytical reports", "Multi-language support"]}}], "total": 3}