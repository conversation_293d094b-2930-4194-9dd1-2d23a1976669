// بيانات المشاريع - تم إنشاؤها تلقائياً من projects.json
window.projectsData = {
    "success": true,
    "projects": [
        {
            "id": "psychai",
            "title": {
                "ar": "Psych AI - منصة الدعم النفسي الذكية",
                "en": "Psych AI - Smart Mental Health Support Platform"
            },
            "description": {
                "ar": "منصة مبتكرة تجمع بين الذكاء الاصطناعي والدعم النفسي، مصممة لتقديم المساعدة والدعم النفسي باللغة العربية باستخدام تقنية Google Gemini AI",
                "en": "Innovative platform that combines artificial intelligence with mental health support, designed to provide psychological assistance in Arabic using Google Gemini AI technology"
            },
            "detailedDescription": {
                "ar": "منصة مبتكرة تجمع بين الذكاء الاصطناعي والدعم النفسي، مصممة لتقديم المساعدة والدعم النفسي باللغة العربية. المنصة تستخدم تقنية Google Gemini AI لتوفير تجربة محادثة طبيعية وفعالة مع مساعد ذكي يفهم احتياجاتك النفسية. تتضمن المنصة مقالات متخصصة من متخصصين معتمدين، فيديوهات تعليمية، ونظام تعليقات للتفاعل المجتمعي.",
                "en": "Innovative platform that combines artificial intelligence with mental health support, designed to provide psychological assistance in Arabic. The platform utilizes Google Gemini AI technology to deliver a natural and effective conversational experience with an AI assistant that understands your psychological needs. The platform includes expert articles from certified professionals, educational videos, and a comment system for community interaction."
            },
            "url": "/PsychAI/",
            "icon": "🧠",
            "status": "active",
            "technologies": [
                "Laravel 12.x",
                "PHP 8.2+",
                "Google Gemini AI",
                "MySQL/PostgreSQL",
                "Node.js",
                "Composer",
                "JavaScript"
            ],
            "startDate": "2024-01-15",
            "lastUpdated": "2024-12-15",
            "features": {
                "ar": [
                    "محادثة ذكية مع مساعد AI يفهم احتياجاتك النفسية",
                    "مقالات متخصصة من متخصصين معتمدين",
                    "فيديوهات تعليمية لفهم القضايا النفسية",
                    "نظام تعليقات للتفاعل المجتمعي",
                    "لوحة تحكم شاملة للإدارة",
                    "دعم متعدد الأدوار (مدير، طبيب، مستخدم)",
                    "واجهة باللغة العربية بالكامل"
                ],
                "en": [
                    "Smart chat with AI assistant that understands psychological needs",
                    "Expert articles from certified professionals",
                    "Educational videos for understanding mental health issues",
                    "Comment system for community interaction",
                    "Comprehensive admin dashboard",
                    "Multi-role support (admin, doctor, user)",
                    "Full Arabic language interface"
                ]
            }
        },
        {
            "id": "schoolproject11",
            "title": {
                "ar": "نظام إدارة المدرسة المتقدم",
                "en": "Advanced School Management System"
            },
            "description": {
                "ar": "نظام إدارة مدرسية متكامل مبني بـ Laravel و Vue.js مع نظام محادثات في الوقت الفعلي وإدارة متقدمة للأدوار والصلاحيات",
                "en": "Integrated school management system built with Laravel & Vue.js featuring real-time chat system and advanced role-based permissions"
            },
            "detailedDescription": {
                "ar": "نظام إدارة مدرسية متطور مبني باستخدام Laravel 11 و Vue.js 3 مع Tailwind CSS. يوفر النظام حلولاً شاملة لإدارة العملية التعليمية بما في ذلك إدارة الطلاب والمعلمين والمواد الدراسية. يتميز النظام بنظام محادثات في الوقت الفعلي باستخدام Pusher، وإدارة متقدمة للأدوار والصلاحيات باستخدام Spatie Laravel Permission، وواجهة مستخدم حديثة ومتجاوبة مع جداول بيانات تفاعلية.",
                "en": "Advanced school management system built using Laravel 11 and Vue.js 3 with Tailwind CSS. The system provides comprehensive solutions for educational process management including student, teacher, and subject management. Features real-time chat system using Pusher, advanced role and permission management using Spatie Laravel Permission, and modern responsive user interface with interactive data tables."
            },
            "url": "/SchoolProject11/",
            "icon": "🏫",
            "status": "active",
            "technologies": [
                "Laravel 11.x",
                "Vue.js 3",
                "PHP 8.2+",
                "Tailwind CSS",
                "Pusher (WebSocket)",
                "Spatie Laravel Permission",
                "DataTables",
                "PrimeVue",
                "Pinia (State Management)"
            ],
            "startDate": "2024-03-10",
            "lastUpdated": "2024-12-10",
            "features": {
                "ar": [
                    "إدارة شاملة للطلاب والمعلمين والمواد الدراسية",
                    "نظام محادثات في الوقت الفعلي باستخدام Pusher",
                    "إدارة متقدمة للأدوار والصلاحيات",
                    "واجهة مستخدم حديثة مع Vue.js 3 و Tailwind CSS",
                    "جداول بيانات تفاعلية مع DataTables",
                    "نظام إدارة الحالة باستخدام Pinia",
                    "واجهة متجاوبة لجميع الأجهزة",
                    "مكونات UI متقدمة مع PrimeVue"
                ],
                "en": [
                    "Comprehensive management for students, teachers, and subjects",
                    "Real-time chat system using Pusher",
                    "Advanced role and permission management",
                    "Modern UI with Vue.js 3 and Tailwind CSS",
                    "Interactive data tables with DataTables",
                    "State management system using Pinia",
                    "Responsive interface for all devices",
                    "Advanced UI components with PrimeVue"
                ]
            }
        },
        {
            "id": "dynamitego",
            "title": {
                "ar": "منصة خدمات ديناميت",
                "en": "Dynamite Services Platform"
            },
            "description": {
                "ar": "سوق متعدد الخدمات يربط المستخدمين بمقدمي الخدمات في فئات متنوعة مع خرائط تفاعلية ونظام حجوزات متكامل",
                "en": "Multi-service marketplace connecting users with service providers across various categories with interactive maps and integrated booking system"
            },
            "detailedDescription": {
                "ar": "منصة خدمات ديناميت هي سوق متعدد الخدمات يهدف إلى ربط المستخدمين بمقدمي الخدمات في فئات متنوعة مثل الخدمات المنزلية، النقل، الصحة، والتعليم. تمكن المنصة المستخدمين من تصفح الخدمات عبر خرائط تفاعلية، التواصل الفوري، تلقي الإشعارات، وإدارة حجوزاتهم بسلاسة عبر واجهات الويب وتطبيق الموبايل. تتميز المنصة بنظام تحكم دقيق للأدوار والصلاحيات، ولوحات تحكم قوية للمستخدمين والمزودين والإداريين.",
                "en": "Dynamite Services Platform is a multi-service marketplace aimed at connecting users with service providers across various categories such as home services, transportation, healthcare, and education. The platform enables users to browse services through interactive maps, instant communication, receive notifications, and manage their bookings seamlessly through web interfaces and mobile applications. The platform features precise role-based access control and powerful dashboards for users, providers, and administrators."
            },
            "url": "/DynamiteGo/",
            "icon": "🧨",
            "status": "development",
            "technologies": [
                "Laravel (PHP)",
                "Vue.js",
                "Flutter",
                "MySQL",
                "Firebase",
                "AWS S3",
                "Google Maps API",
                "Pusher (Real-time)",
                "Redis"
            ],
            "startDate": "2024-05-20",
            "lastUpdated": "2024-12-08",
            "features": {
                "ar": [
                    "دمج الخرائط - بحث وعرض مقدمي الخدمات حسب الموقع الجغرافي",
                    "رسائل فورية - دردشة في الوقت الفعلي بين المستخدمين ومقدمي الخدمات",
                    "نظام الإشعارات - إشعارات فورية عبر الموبايل والويب",
                    "إدارة متكاملة - لوحات تحكم قوية لجميع أطراف المنصة",
                    "أدوار وصلاحيات - نظام تحكم دقيق (RBAC) لضمان الأمان",
                    "تطبيق متعدد المنصات - تجربة متكاملة عبر الويب و iOS/Android",
                    "نظام حجوزات متقدم - إدارة سلسة للحجوزات والمواعيد",
                    "تقييمات ومراجعات - نظام تقييم شامل لضمان الجودة"
                ],
                "en": [
                    "Maps Integration - Search and display service providers by geographic location",
                    "Instant Messaging - Real-time chat between users and service providers",
                    "Notification System - Instant notifications via mobile and web",
                    "Integrated Management - Powerful dashboards for all platform stakeholders",
                    "Roles & Permissions - Precise access control (RBAC) for security",
                    "Cross-platform App - Integrated experience across web and iOS/Android",
                    "Advanced Booking System - Seamless booking and appointment management",
                    "Reviews & Ratings - Comprehensive rating system for quality assurance"
                ]
            }
        }
    ],
    "total": 3
};
