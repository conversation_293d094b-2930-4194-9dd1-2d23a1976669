// بيانات المشاريع - تم إنشاؤها تلقائياً من projects.json
window.projectsData = {
    "success": true,
    "projects": [
        {
            "id": "psychai",
            "title": {
                "ar": "Psych AI - منصة الدعم النفسي الذكية",
                "en": "Psych AI - Smart Mental Health Support Platform"
            },
            "description": {
                "ar": "منصة مبتكرة تجمع بين الذكاء الاصطناعي والدعم النفسي، مصممة لتقديم المساعدة والدعم النفسي باللغة العربية باستخدام تقنية Google Gemini AI",
                "en": "Innovative platform that combines artificial intelligence with mental health support, designed to provide psychological assistance in Arabic using Google Gemini AI technology"
            },
            "detailedDescription": {
                "ar": "منصة مبتكرة تجمع بين الذكاء الاصطناعي والدعم النفسي، مصممة لتقديم المساعدة والدعم النفسي باللغة العربية. المنصة تستخدم تقنية Google Gemini AI لتوفير تجربة محادثة طبيعية وفعالة مع مساعد ذكي يفهم احتياجاتك النفسية. تتضمن المنصة مقالات متخصصة من متخصصين معتمدين، فيديوهات تعليمية، ونظام تعليقات للتفاعل المجتمعي.",
                "en": "Innovative platform that combines artificial intelligence with mental health support, designed to provide psychological assistance in Arabic. The platform utilizes Google Gemini AI technology to deliver a natural and effective conversational experience with an AI assistant that understands your psychological needs. The platform includes expert articles from certified professionals, educational videos, and a comment system for community interaction."
            },
            "url": "/PsychAI/",
            "icon": "🧠",
            "status": "active",
            "technologies": [
                "Laravel 12.x",
                "PHP 8.2+",
                "Google Gemini AI",
                "MySQL/PostgreSQL",
                "Node.js",
                "Composer",
                "JavaScript"
            ],
            "startDate": "2024-01-15",
            "lastUpdated": "2024-12-15",
            "features": {
                "ar": [
                    "محادثة ذكية مع مساعد AI يفهم احتياجاتك النفسية",
                    "مقالات متخصصة من متخصصين معتمدين",
                    "فيديوهات تعليمية لفهم القضايا النفسية",
                    "نظام تعليقات للتفاعل المجتمعي",
                    "لوحة تحكم شاملة للإدارة",
                    "دعم متعدد الأدوار (مدير، طبيب، مستخدم)",
                    "واجهة باللغة العربية بالكامل"
                ],
                "en": [
                    "Smart chat with AI assistant that understands psychological needs",
                    "Expert articles from certified professionals",
                    "Educational videos for understanding mental health issues",
                    "Comment system for community interaction",
                    "Comprehensive admin dashboard",
                    "Multi-role support (admin, doctor, user)",
                    "Full Arabic language interface"
                ]
            }
        },
        {
            "id": "schoolproject11",
            "title": {
                "ar": "نظام إدارة المدرسة المتقدم",
                "en": "Advanced School Management System"
            },
            "description": {
                "ar": "نظام إدارة مدرسية متكامل مبني بـ Laravel و Vue.js مع نظام محادثات في الوقت الفعلي وإدارة متقدمة للأدوار والصلاحيات",
                "en": "Integrated school management system built with Laravel & Vue.js featuring real-time chat system and advanced role-based permissions"
            },
            "detailedDescription": {
                "ar": "نظام إدارة مدرسية متطور مبني باستخدام Laravel 11 و Vue.js 3 مع Tailwind CSS. يوفر النظام حلولاً شاملة لإدارة العملية التعليمية بما في ذلك إدارة الطلاب والمعلمين والمواد الدراسية. يتميز النظام بنظام محادثات في الوقت الفعلي باستخدام Pusher، وإدارة متقدمة للأدوار والصلاحيات باستخدام Spatie Laravel Permission، وواجهة مستخدم حديثة ومتجاوبة مع جداول بيانات تفاعلية.",
                "en": "Advanced school management system built using Laravel 11 and Vue.js 3 with Tailwind CSS. The system provides comprehensive solutions for educational process management including student, teacher, and subject management. Features real-time chat system using Pusher, advanced role and permission management using Spatie Laravel Permission, and modern responsive user interface with interactive data tables."
            },
            "url": "/SchoolProject11/",
            "icon": "🏫",
            "status": "active",
            "technologies": [
                "Laravel 11.x",
                "Vue.js 3",
                "PHP 8.2+",
                "Tailwind CSS",
                "Pusher (WebSocket)",
                "Spatie Laravel Permission",
                "DataTables",
                "PrimeVue",
                "Pinia (State Management)"
            ],
            "startDate": "2024-03-10",
            "lastUpdated": "2024-12-10",
            "features": {
                "ar": [
                    "إدارة شاملة للطلاب والمعلمين والمواد الدراسية",
                    "نظام محادثات في الوقت الفعلي باستخدام Pusher",
                    "إدارة متقدمة للأدوار والصلاحيات",
                    "واجهة مستخدم حديثة مع Vue.js 3 و Tailwind CSS",
                    "جداول بيانات تفاعلية مع DataTables",
                    "نظام إدارة الحالة باستخدام Pinia",
                    "واجهة متجاوبة لجميع الأجهزة",
                    "مكونات UI متقدمة مع PrimeVue"
                ],
                "en": [
                    "Comprehensive management for students, teachers, and subjects",
                    "Real-time chat system using Pusher",
                    "Advanced role and permission management",
                    "Modern UI with Vue.js 3 and Tailwind CSS",
                    "Interactive data tables with DataTables",
                    "State management system using Pinia",
                    "Responsive interface for all devices",
                    "Advanced UI components with PrimeVue"
                ]
            }
        },
        {
            "id": "dynamitego",
            "title": {
                "ar": "DynamiteGo - تطبيق الخرائط التفاعلي",
                "en": "DynamiteGo - Interactive Maps Application"
            },
            "description": {
                "ar": "تطبيق Flutter متقدم للخرائط التفاعلية مصمم لتعلم تطوير تطبيقات الهاتف المحمول مع التركيز على تقنيات الخرائط والموقع الجغرافي",
                "en": "Advanced Flutter application for interactive maps designed for learning mobile app development with focus on mapping and geolocation technologies"
            },
            "detailedDescription": {
                "ar": "مشروع تطبيق Flutter مبتكر يهدف إلى تعلم وإتقان تطوير تطبيقات الهاتف المحمول باستخدام إطار عمل Flutter. يركز التطبيق على تقنيات الخرائط التفاعلية والموقع الجغرافي، ويتضمن ميزات متقدمة مثل تتبع الموقع في الوقت الفعلي، البحث عن الأماكن، التنقل والاتجاهات، وحفظ الأماكن المفضلة. المشروع يستخدم أحدث تقنيات Flutter وGoogle Maps API لتوفير تجربة مستخدم سلسة ومتطورة.",
                "en": "Innovative Flutter application project aimed at learning and mastering mobile app development using the Flutter framework. The app focuses on interactive mapping and geolocation technologies, featuring advanced capabilities such as real-time location tracking, place search, navigation and directions, and favorite places management. The project utilizes the latest Flutter technologies and Google Maps API to provide a smooth and advanced user experience."
            },
            "url": "/DynamiteGo/",
            "icon": "🗺️",
            "status": "development",
            "technologies": [
                "Flutter",
                "Dart",
                "Google Maps API",
                "Firebase",
                "Geolocator",
                "HTTP Package",
                "Provider/Bloc",
                "SQLite"
            ],
            "startDate": "2024-05-20",
            "lastUpdated": "2024-12-08",
            "features": {
                "ar": [
                    "خرائط تفاعلية باستخدام Google Maps",
                    "تتبع الموقع في الوقت الفعلي",
                    "البحث عن الأماكن والمواقع",
                    "نظام التنقل والاتجاهات",
                    "حفظ الأماكن المفضلة",
                    "واجهة مستخدم حديثة ومتجاوبة",
                    "دعم أنظمة Android و iOS",
                    "تكامل مع Firebase للبيانات السحابية"
                ],
                "en": [
                    "Interactive maps using Google Maps",
                    "Real-time location tracking",
                    "Place and location search",
                    "Navigation and directions system",
                    "Favorite places management",
                    "Modern and responsive user interface",
                    "Android and iOS support",
                    "Firebase integration for cloud data"
                ]
            }
        }
    ],
    "total": 3
};
