<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مشاريعي - Projects Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .project-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .project-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .project-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
            color: white;
        }

        .project-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .project-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .project-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-active {
            background: #e8f5e8;
            color: #2d5a2d;
        }

        .status-development {
            background: #fff3cd;
            color: #856404;
        }

        .footer {
            text-align: center;
            margin-top: 60px;
            color: white;
            opacity: 0.8;
        }

        .language-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .language-toggle:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        body.en {
            direction: ltr;
        }

        body.en .language-toggle {
            left: auto;
            right: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading-animation {
            animation: spin 2s linear infinite;
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease forwards;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .projects-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .project-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <button class="language-toggle" onclick="toggleLanguage()" id="langToggle">English</button>

    <div class="container">
        <div class="header">
            <h1 id="mainTitle">🚀 مشاريعي</h1>
            <p id="mainSubtitle">مرحباً بك في لوحة تحكم المشاريع - اختر المشروع الذي تريد الوصول إليه</p>
        </div>

        <div class="projects-grid" id="projectsGrid">
            <!-- سيتم تحميل المشاريع هنا تلقائياً -->
        </div>

        <div class="footer">
            <p>&copy; 2024 - تم إنشاؤها بواسطة نظام إدارة المشاريع التلقائي</p>
        </div>
    </div>

    <script>
        // دالة لإنشاء بطاقة المشروع
        function createProjectCard(project) {
            // استخدام معرف المشروع إذا كان متوفراً، وإلا استخدام العنوان
            const projectId = project.id || project.title.toLowerCase().replace(/\s+/g, '');
            const title = typeof project.title === 'object' ? project.title[currentLanguage] : project.title;
            const description = typeof project.description === 'object' ? project.description[currentLanguage] : project.description;

            const statusText = project.status === 'active'
                ? (currentLanguage === 'ar' ? 'نشط' : 'Active')
                : (currentLanguage === 'ar' ? 'قيد التطوير' : 'In Development');

            const detailsText = currentLanguage === 'ar' ? 'عرض التفاصيل' : 'View Details';
            const visitText = currentLanguage === 'ar' ? 'زيارة المشروع' : 'Visit Project';

            return `
                <div class="project-card" onclick="goToProjectDetails('${projectId}')">
                    <div class="project-icon">${project.icon}</div>
                    <div class="project-title">${title}</div>
                    <div class="project-description">${description}</div>
                    <span class="project-status ${project.status === 'active' ? 'status-active' : 'status-development'}">
                        ${statusText}
                    </span>
                    <div style="margin-top: 15px; display: flex; gap: 10px;">
                        <button onclick="event.stopPropagation(); goToProjectDetails('${projectId}')"
                                style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 0.9rem;">
                            ${detailsText}
                        </button>
                        <button onclick="event.stopPropagation(); window.open('${project.url}', '_blank')"
                                style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 20px; cursor: pointer; font-size: 0.9rem;">
                            ${visitText}
                        </button>
                    </div>
                </div>
            `;
        }

        // دالة للانتقال إلى صفحة تفاصيل المشروع
        function goToProjectDetails(projectId) {
            window.location.href = `/project-details.html?id=${projectId}`;
        }

        // دالة لعرض رسالة الخطأ
        function showError(message) {
            const projectsGrid = document.getElementById('projectsGrid');
            projectsGrid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; color: white; padding: 40px;">
                    <div style="font-size: 3rem; margin-bottom: 20px;">⚠️</div>
                    <h3 style="margin-bottom: 10px;">خطأ في تحميل المشاريع</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        // دالة لعرض رسالة التحميل
        function showLoading() {
            const loadingText = currentLanguage === 'ar' ? 'جاري تحميل المشاريع...' : 'Loading projects...';
            const projectsGrid = document.getElementById('projectsGrid');
            projectsGrid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; color: white; padding: 40px;">
                    <div style="font-size: 3rem; margin-bottom: 20px; animation: spin 2s linear infinite;">⚙️</div>
                    <h3>${loadingText}</h3>
                </div>
            `;
        }

        // تحميل المشاريع من ملف JSON
        async function loadProjects() {
            try {
                showLoading();

                const response = await fetch('/projects.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.error || (currentLanguage === 'ar' ? 'خطأ غير معروف' : 'Unknown error'));
                }

                projectsData = data;
                renderProjects(data);

            } catch (error) {
                console.error('Error loading projects:', error);
                showError(error.message);
            }
        }

        // دالة عرض المشاريع
        function renderProjects(data) {
            const projectsGrid = document.getElementById('projectsGrid');
            projectsGrid.innerHTML = '';

            if (data.projects.length === 0) {
                const noProjectsText = currentLanguage === 'ar'
                    ? { title: 'لا توجد مشاريع', desc: 'لم يتم العثور على أي مشاريع في المجلد الحالي' }
                    : { title: 'No Projects Found', desc: 'No projects were found in the current directory' };

                projectsGrid.innerHTML = `
                    <div style="grid-column: 1 / -1; text-align: center; color: white; padding: 40px;">
                        <div style="font-size: 3rem; margin-bottom: 20px;">📁</div>
                        <h3 style="margin-bottom: 10px;">${noProjectsText.title}</h3>
                        <p>${noProjectsText.desc}</p>
                    </div>
                `;
                return;
            }

            data.projects.forEach(project => {
                projectsGrid.innerHTML += createProjectCard(project);
            });

            // إضافة تأثير التحميل
            const cards = document.querySelectorAll('.project-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        }

        // متغيرات اللغة
        let currentLanguage = localStorage.getItem('preferredLanguage') || 'ar';
        let projectsData = null;

        // تحميل المشاريع عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            applyLanguage();
            loadProjects();
        });

        // دالة تبديل اللغة
        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            localStorage.setItem('preferredLanguage', currentLanguage);
            applyLanguage();
            if (projectsData) {
                renderProjects(projectsData);
            }
        }

        // دالة تطبيق اللغة
        function applyLanguage() {
            const body = document.body;
            const html = document.documentElement;

            if (currentLanguage === 'en') {
                body.classList.add('en');
                html.setAttribute('lang', 'en');
                html.setAttribute('dir', 'ltr');

                document.getElementById('mainTitle').textContent = '🚀 My Projects';
                document.getElementById('mainSubtitle').textContent = 'Welcome to the projects dashboard - Choose the project you want to access';
                document.getElementById('langToggle').textContent = 'العربية';
            } else {
                body.classList.remove('en');
                html.setAttribute('lang', 'ar');
                html.setAttribute('dir', 'rtl');

                document.getElementById('mainTitle').textContent = '🚀 مشاريعي';
                document.getElementById('mainSubtitle').textContent = 'مرحباً بك في لوحة تحكم المشاريع - اختر المشروع الذي تريد الوصول إليه';
                document.getElementById('langToggle').textContent = 'English';
            }
        }



        // إضافة تأثيرات تفاعلية
        document.addEventListener('click', function(e) {
            if (e.target.closest('.project-card')) {
                const card = e.target.closest('.project-card');
                card.style.transform = 'scale(0.95)';

                setTimeout(() => {
                    card.style.transform = '';
                }, 150);
            }
        });
    </script>
</body>
</html>
