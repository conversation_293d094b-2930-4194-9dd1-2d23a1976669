<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// قراءة ملف JSON
$jsonFile = __DIR__ . '/projects.json';

if (!file_exists($jsonFile)) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'error' => 'Projects file not found'
    ]);
    exit;
}

$jsonContent = file_get_contents($jsonFile);
$data = json_decode($jsonContent, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Invalid JSON format: ' . json_last_error_msg()
    ]);
    exit;
}

// إضافة timestamp لمنع التخزين المؤقت
$data['timestamp'] = time();

echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
