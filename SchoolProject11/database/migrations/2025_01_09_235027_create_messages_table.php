<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            // $table->foreignId('conversation_id');
            // $table->foreignId('sender_user_id');
            // $table->foreignId('receiver_user_id');
            // $table->text('message_text');
            // $table->foreign('conversation_id')->references('id')->on('conversations')->onDelete('cascade');
            // $table->foreign('sender_user_id')->references('id')->on('users')->onDelete('cascade');
            // $table->foreign('receiver_user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreignId('conversation_id')->constrained('conversations')->onDelete('cascade');
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->text('text');
            $table->boolean('is_read')->default(false);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
