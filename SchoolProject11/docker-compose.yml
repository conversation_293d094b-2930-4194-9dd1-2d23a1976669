version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: laravel_app
    working_dir: /var/www
    volumes:
      - .:/var/www
    networks:
      - app_network
    depends_on:
      - db
    ports:
      - "8000:80"
    environment:
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=your_database_name
      - DB_USERNAME=root
      - DB_PASSWORD=your_password
      - APP_KEY=base64:your_app_key_here

  db:
    image: mysql:5.7
    container_name: mysql_db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: your_password
      MYSQL_DATABASE: your_database_name
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - app_network
    ports:
      - "3306:3306"

  nginx:
    image: nginx:latest
    container_name: nginx
    volumes:
      - ./nginx/default.conf:/etc/nginx/nginx.conf
      - .:/var/www
    ports:
      - "80:80"
    depends_on:
      - app
    networks:
      - app_network

volumes:
  mysql_data:

networks:
  app_network:
    driver: bridge
