{"dependencies": {"clone": "^2.1.2", "datatables.net-bs5": "^2.1.8", "datatables.net-dt": "^2.1.8", "datatables.net-responsive-dt": "^3.0.3", "datatables.net-select-dt": "^2.1.0", "datatables.net-vue3": "^3.0.3", "debounce": "^2.2.0", "jquery": "^3.7.1", "merge": "^2.1.1", "pinia": "^2.2.8", "pinia-plugin-persistedstate": "^3.2.0", "primeicons": "^7.0.0", "primevue": "^4.2.5", "router": "^2.0.0", "vue": "^3.5.13", "vue-pagination-2": "^3.1.0", "vue-pagination-3": "^2.0.0-beta01", "vue-router": "^4.0.13", "vue-tables-3": "^2.0.1-beta05", "vue3-table-lite": "^1.4.1"}, "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "autoprefixer": "^10.4.20", "axios": "^1.7.8", "laravel-echo": "^1.19.0", "laravel-vite-plugin": "^1.0.0", "postcss": "^8.4.49", "pusher-js": "^8.4.0", "tailwindcss": "^3.4.15", "unplugin-vue-router": "^0.10.8", "vite": "^5.0.0", "vite-plugin-laravel": "^0.3.1"}, "type": "module"}