import { defineS<PERSON> } from "pinia";
import $, { error, get } from "jquery";



export const useAdminStore = defineStore("admin", {
    state: () => ({
        AllSubjects: [],
        AllUsers: [],
        Errors: [],
    }),
    getters: {
        users: (state) => state.AllUsers,
        subjects: (state) => state.AllSubjects,
        errors: (state) => state.Errors,
    },
    actions: {
        async getUsers() {
            if (this.AllUsers.length > 0) {
                return this.AllUsers;
            } else {
                try {
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            type: "get",
                            url: "/SchoolProject11/api/admin/user ",
                            dataType: "json",
                            success: (data) => {
                                this.AllUsers = data.users;
                                resolve(data);
                            },
                            error: (error) => {
                                const errors =
                                    error.responseJSON?.message ||
                                    "An error occurred";
                                reject(errors);
                            },
                        });
                    });
                } catch (error) {
                    console.error("User Fetch Error:", error);
                    throw error;
                }
            }
        },
        async createUser(data) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "post",
                        url: `/SchoolProject11/api/admin/user`,
                        data: data,
                        success: (response) => {
                            this.AllUsers.push(response.user);
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("User create Error:", error);

                            this.Errors = error.responseJSON.errors;
                            reject(error);
                        },
                    });
                });
            } catch (error) {
                console.error("User create Error:", error);
                throw error;
            }
        },
        async deleteUser(data) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "DELETE",
                        url: `/SchoolProject11/api/admin/user/${data.id}`,
                        success:  (response) => {
                            this.AllUsers = this.AllUsers.filter(
                                (user) => user.id !== data.id
                            );
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("User Delete Error:", error);
                            reject(error);
                        },
                    });
                });
            } catch (error) {
                console.error("Unexpected Error During User Deletion:", error);
                throw error;
            }
        },
        async updateUser(data) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "put",
                        url: `/SchoolProject11/api/admin/user/${data.id}`,
                        data: {
                            roles: data.roles[0].name,
                        },
                        xhrFields: { withCredentials: true },
                        headers: {
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        success: (response) => {
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("User Update Error:", error);
                            reject(error);
                        }
                    });
                });
            } catch (error) {
                console.error("User Update Error:", error);
                throw error;
            }
        },
        async getSubjects() {
            if (this.AllSubjects.length > 0) {
                return this.AllSubjects;
            } else {
                try {
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            type: "get",
                            url: "/SchoolProject11/api/admin/subject ",
                            dataType: "json",
                            success: (data) => {
                                this.AllSubjects = data.subjects;
                                resolve(data);
                            },
                            error: (error) => {
                                const errors =
                                    error.responseJSON?.message ||
                                    "An error occurred";
                                reject(error);
                            },
                        });
                    });
                } catch (error) {
                    console.error("User Fetch Error:", error);
                    throw error;
                }
            }
        },
        async createSubject(data) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "post",
                        url: `/SchoolProject11/api/admin/subject`,
                        data: data,
                        success: (response) => {
                            this.AllSubjects.push(response.subject);
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("subject create Error:", error);
                            this.Errors = error.responseJSON.errors;
                            reject(error);
                        },
                    });
                });
            } catch (error) {
                console.error("subject create Error:", error);
                throw error;
            }
        },
        async updateSubject(data) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "put",
                        url: `/SchoolProject11/api/admin/subject/${data.id}`,
                        data: {
                            name: data.name,
                            success_mark: data.success_mark,
                            full_mark: data.full_mark,
                        },
                        success: (response) => {
                            // this.AllSubjects[response.subject.id] = response.subject

                            resolve(response);
                        },
                        error: (error) => {
                            console.error("subject Update Error:", error);
                            reject(error);
                        },
                    });
                });
            } catch (error) {
                console.error("subject Update Error:", error);
                throw error;
            }
        },
        async deleteSubject(data) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "DELETE",
                        url: `/SchoolProject11/api/admin/subject/${data.id}`,
                        success: async (response) => {
                            this.AllSubjects = this.AllSubjects.filter(
                                (subject) => subject.id !== data.id
                            );
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("Subject Delete Error:", error);
                            reject(error);
                        },
                    });
                });
            } catch (error) {
                console.error(
                    "Unexpected Error During Subject Deletion:",
                    error
                );
                throw error;
            }
        },
        async createSubjectUsers(subject_id, data) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "post",
                        url: `/SchoolProject11/api/admin/subjectUsers/${subject_id}`,
                        data: {
                            user_ids: data[0],
                            subject_id: subject_id,
                        },
                        success: (response) => {
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("subject create Error:", error);

                            this.Errors = error.responseJSON.errors;
                            reject(error);
                        },
                    });
                });
            } catch (error) {
                console.error("subject create Error:", error);
                throw error;
            }
        },
        async updateSubjectUsers(data) {
            try {

                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "put",
                        url: `/SchoolProject11/api/admin/subjectUsers/${data.subject_id}/${data.user_id}`,
                        data: data,
                        success: (response) => {
                            // this.AllSubjects[response.subject.id]["users"].push(response.subject);
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("subject create Error:", error);

                            this.Errors = error.responseJSON.errors;
                            reject(error);
                        },
                    });
                });
            } catch (error) {
                console.error("subject create Error:", error);
                throw error;
            }
        },
        async deleteSubjectUsers(data) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "delete",
                        url: `/SchoolProject11/api/admin/subjectUsers/${data.subject_id}/${data.user_id}`,
                        data: data,
                        success: (response) => {
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("subject create Error:", error);
                            this.Errors = error.responseJSON.errors;
                            reject(error);
                        },
                    });
                });
            } catch (error) {
                console.error("subject create Error:", error);
                throw error;
            }
        },

        clearErrors() {
            this.Errors = {}; // طريقة لإعادة تعيين الأخطاء
        },
    },
});
