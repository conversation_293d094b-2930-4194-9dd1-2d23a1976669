import { defineS<PERSON> } from "pinia";
import $, { error, get } from "jquery";



export const useMessageStore = defineStore("message", {
    state: () => ({
        AllConversations: [],
        Errors: [],
    }),
    getters: {
        conversations: (state) => state.AllConversations,
        quryConversations: (state) => state.QuryConversations,
        errors: (state) => state.Errors,
    },
    actions: {
        async getConversations() {
            if (this.AllConversations.length > 0) {
                return this.AllConversations;
            } else {
                try {
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            type: "GET",
                            url: "/SchoolProject11/api/conversation",
                            dataType: "json",
                            success: (response) => {
                                this.AllConversations = response.data;
                                resolve(response);
                            },
                            error: (error) => {
                                const errors =
                                    error.responseJSON?.message ||
                                    "An error occurred";
                                reject(errors);
                            },
                        });
                    });
                } catch (error) {
                    console.error("User Fetch Error:", error);
                    throw error;
                }
            }
        },
        async searchConversations(serch) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "GET",
                        url: "/SchoolProject11/api/conversation/search",
                        dataType: "json",
                        data: {
                            serch: serch,
                        },
                        success: (response) => {
                            resolve(response);
                        },
                    });
                });
            } catch (error) {
                console.error("User Fetch Error:", error);
                throw error;
            }
        },
        async createConversation(userId) {

            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "POST",
                        url: "/SchoolProject11/api/conversation",
                        data: {
                            user_two_id: userId,
                        },
                        success: (response) => {
                            // this.AllConversations.push(response.conversation);
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("Error:", error);
                        },
                    });
                });
            } catch (error) {
                console.error("User Fetch Error:", error);
                throw error;
            }
        },
        async createMessage(data) {


            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "POST",
                        url: "/SchoolProject11/api/message",
                        data: {
                            conversation_id: data["conversationId"],
                            text: data["text"],
                            created_at: data["created_at"],
                        },
                        success: (response) => {
                            resolve(response);
                        },
                        error: (error) => {
                            console.error("Error:", error.responseJSON.message);
                        },
                    });
                });
            } catch (error) {
                console.error("User Fetch Error:", error);
                throw error;
            }
        },
        async editCheckValueInMessage(conversationId) {
            try {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: "put",
                        url: `/SchoolProject11/api/conversation/${conversationId}/isRead`,
                        dataType: "json",
                        success: (response) => {
                            resolve(response);
                        },
                    });
                });
            } catch (error) {
                console.error("User Fetch Error:", error);
                throw error;
            }
        },
    },
});
