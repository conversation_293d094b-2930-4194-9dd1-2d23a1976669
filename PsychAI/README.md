<div align="center">

# Psych AI - منصة الدعم النفسي الذكية 🧠
# Psych AI - Smart Mental Health Support Platform 🧠

[العربية](#arabic) | [English](#english)

</div>

<div dir="rtl" id="arabic">

# النسخة العربية 🌟

## نظرة عامة
Psych AI هي منصة مبتكرة تجمع بين الذكاء الاصطناعي والدعم النفسي، مصممة لتقديم المساعدة والدعم النفسي باللغة العربية. المنصة تستخدم تقنية Google Gemini AI لتوفير تجربة محادثة طبيعية وفعالة.

## المميزات الرئيسية ✨
- **محادثة ذكية**: دردشة تفاعلية مع مساعد ذكي يفهم احتياجاتك النفسية
- **مقالات متخصصة**: محتوى نفسي موثوق من متخصصين معتمدين
- **فيديوهات تعليمية**: محتوى مرئي يساعد في فهم القضايا النفسية
- **نظام تعليقات**: تفاعل مجتمعي مع المحتوى والمقالات
- **لوحة تحكم للإدارة**: إدارة كاملة للمحتوى والمستخدمين
- **دعم متعدد الأدوار**: أدوار مختلفة للمستخدمين (مدير، طبيب، مستخدم عادي)

## المتطلبات التقنية 🛠
- PHP 8.2 أو أحدث
- Laravel 12.x
- Node.js & NPM
- MySQL/PostgreSQL
- Composer
- Google Gemini API Key

## خطوات التثبيت 📥

1. **استنساخ المشروع**
```bash
git clone https://github.com/yourusername/psych-ai.git
cd psych-ai
```

2. **تثبيت اعتماديات PHP**
```bash
composer install
```

3. **تثبيت اعتماديات Node.js**
```bash
npm install
```

4. **إعداد ملف البيئة**
```bash
cp .env.example .env
php artisan key:generate
```

## المساهمة 🤝
نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. تقديم Pull Request

## الترخيص 📄
 

</div>

---

<div id="english">

# English Version 🌟

## Overview
Psych AI is an innovative platform that combines artificial intelligence with mental health support, designed to provide psychological assistance in Arabic. The platform utilizes Google Gemini AI technology to deliver a natural and effective conversational experience.

## Key Features ✨
- **Smart Chat**: Interactive conversations with an AI assistant that understands your psychological needs
- **Expert Articles**: Trusted mental health content from certified professionals
- **Educational Videos**: Visual content helping understand mental health issues
- **Comment System**: Community interaction with content and articles
- **Admin Dashboard**: Complete content and user management
- **Multi-Role Support**: Different user roles (admin, doctor, regular user)

## Technical Requirements 🛠
- PHP 8.2 or higher
- Laravel 12.x
- Node.js & NPM
- MySQL/PostgreSQL
- Composer
- Google Gemini API Key

## Installation Steps 📥

1. **Clone the Project**
```bash
git clone https://github.com/yourusername/psych-ai.git
cd psych-ai
```

2. **Install PHP Dependencies**
```bash
composer install
```

3. **Install Node.js Dependencies**
```bash
npm install
```

4. **Setup Environment File**
```bash
cp .env.example .env
php artisan key:generate
```

## Project Structure 📁
```
├── app/
│   ├── Http/
│   ├── Models/
│   └── Services/
├── resources/
│   ├── views/
│   ├── js/
│   └── css/
└── routes/
```

## Configuration ⚙️

### Setting up Gemini AI
1. Get your API key from [Google AI Studio](https://makersuite.google.com)
2. Add the key to your `.env` file:
```env
GEMINI_API_KEY=your_api_key_here
```

## Contributing 🤝
We welcome contributions! Please follow these steps:
1. Fork the project
2. Create a feature branch
3. Submit a Pull Request

## License 📄
This project is licensed under the [MIT License](LICENSE)

## Acknowledgments 💝
- Special thanks to Google Gemini AI team
- All project contributors
- Laravel community

## Contact 📞
- For questions and inquiries: [<EMAIL>]


</div>
