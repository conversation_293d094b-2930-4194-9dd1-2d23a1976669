<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('room_chats_id')->constrained()->onDelete('cascade'); // معرف الغرفة (مفتاح خارجي)
            $table->unsignedBigInteger('sender_id'); // معرف المرسل
            $table->text('message_text'); // نص الرسالة
            $table->text("reseve_text");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
