# منصة الدعم النفسي الذكية - Psych AI 🧠

## نظرة عامة
منصة Psych AI هي منصة مبتكرة تجمع بين الذكاء الاصطناعي والدعم النفسي، مصممة خصيصاً لتقديم المساعدة والدعم النفسي باللغة العربية. تستخدم المنصة تقنية Google Gemini AI لتوفير تجربة محادثة طبيعية وفعالة.

## الصفحات الرئيسية

### 1. الصفحة الرئيسية (/)
- صفحة الترحيب الرئيسية مع واجهة سهلة الاستخدام
- المميزات:
  - زر "فضفض معنا" للدردشة المباشرة
  - عرض التصنيفات النفسية
  - واجهة سهلة الاستخدام

### 2. صفحة المحادثة الذكية (/chat)
- محادثة مباشرة مع المساعد الذكي
- المميزات:
  - محادثة طبيعية باللغة العربية
  - دعم تقنية Google Gemini AI
  - حفظ سجل المحادثات
  - اقتراحات مواضيع المحادثة

### 3. صفحة المختصين (/specialists)
- عرض قائمة المختصين النفسيين
- المميزات:
  - بطاقات عرض المختصين
  - تصفية حسب التخصص
  - نظام حجز المواعيد
  - تقييمات وآراء المستخدمين

### 4. لوحة التحكم (/admin)
#### صلاحيات المدير:
- إدارة المستخدمين
- إدارة المحتوى
- التقارير والإحصائيات
- إدارة المختصين

#### صلاحيات المختص:
- إدارة المواعيد
- إدارة الملف الشخصي
- متابعة الحالات
- نشر المقالات

#### صلاحيات المستخدم:
- المواعيد المحجوزة
- سجل المحادثات
- الملف الشخصي

### 5. صفحة المقالات (/articles)
- مقالات توعوية في الصحة النفسية
- المميزات:
  - تصنيف المقالات
  - نظام التعليقات
  - مشاركة المقالات
  - محتوى موثوق من متخصصين

### 6. صفحة الفيديوهات (/videos)
- محتوى مرئي تعليمي
- المميزات:
  - تصنيف الفيديوهات
  - إمكانية التحميل
  - نظام التعليقات
  - محتوى تعليمي متخصص

### 7. صفحات المصادقة
- تسجيل الدخول
- إنشاء حساب جديد
- استعادة كلمة المرور

## التقنيات المستخدمة

### الواجهة الأمامية:
- قوالب Laravel Blade
- إطار عمل TailwindCSS
- جافا سكريبت وجيكويري
- مكونات Flowbite

### الخلفية:
- إطار عمل Laravel 12.x
- قاعدة بيانات MySQL/PostgreSQL
- واجهة برمجة Google Gemini AI
- نظام مصادقة Laravel Sanctum

## ميزات الأمان
- حماية ضد هجمات CSRF
- مصادقة متعددة العوامل
- تشفير البيانات
- تسجيل الأحداث والنشاطات

## خيارات التخصيص
- سمات قابلة للتخصيص
- دعم اتجاه النص من اليمين لليسار
- واجهة متجاوبة مع جميع الأجهزة
- تخصيص الإشعارات

## الأدوار والصلاحيات

### مدير النظام:
- إدارة كاملة للنظام
- إدارة المستخدمين
- إدارة المحتوى
- مراقبة النظام

### المختص النفسي:
- إدارة الملف الشخصي
- إدارة المواعيد
- نشر المقالات
- متابعة المرضى

### المستخدم العادي:
- حجز المواعيد
- المحادثة مع المساعد الذكي
- التفاعل مع المحتوى
- إدارة الملف الشخصي

## التطويرات المستقبلية
- دمج خدمات الدفع الإلكتروني
- تطوير تطبيق للهواتف الذكية
- إضافة المزيد من اللغات
- إنشاء مجتمع افتراضي للدعم
- تطوير خوارزميات الذكاء الاصطناعي
