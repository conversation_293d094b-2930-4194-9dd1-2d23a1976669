<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Laravel\Passport\ClientRepository;

class PassportClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
         $clientRepository = new ClientRepository();

        // إنشاء Personal Access Client
        $clientRepository->createPersonalAccessClient(
            null,
            'Default Personal Access Client',
            config('app.url')
        );
    }
}
