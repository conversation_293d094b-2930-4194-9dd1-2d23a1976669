<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();
        $this->call([
            RoleSeeder::class,
            PermissionSeeder::class,
            PermissionRoleSeed::class,
        ]);
        User::factory()->create([
            'name' => 'dynamite',
            'email' => '<EMAIL>',
            'password' => bcrypt('123'),
        ])->assignRole('super-admin');
        User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('123'),
        ])->assignRole('admin');
        User::factory()->create([
            'name' => 'User',
            'email' => '<EMAIL>',
            'password' => bcrypt('123'),
        ])->assignRole('user');


    }
}
