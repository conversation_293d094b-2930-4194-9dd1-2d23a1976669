<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PermissionRoleSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $permissions = [
            'create-user',
            'read-user',
            'update-user',
            'delete-user',
            'create-role',
            'read-role',
            'update-role',
            'delete-role',
            'create-permission',
            'read-permission',
            'update-permission',
            'delete-permission',
        ];
        $roles = [
            'admin',
            'user',
            'super-admin'
        ];
        foreach ($roles as $role) {
            foreach ($permissions as $permission) {
                \Spatie\Permission\Models\Role::findByName($role)->givePermissionTo($permission);
            }
        }
        
    }
}
