<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Lara<PERSON>\Passport\Passport;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Configure Passport token expiration
        Passport::tokensExpireIn(now()->addDays(15));
        Passport::refreshTokensExpireIn(now()->addDays(30));
        Passport::personalAccessTokensExpireIn(now()->addMonths(6));

        // Configure Passport encryption keys
        if (file_exists(storage_path('oauth-private.key'))) {
            Passport::loadKeysFrom(storage_path());
        }
    }
}
