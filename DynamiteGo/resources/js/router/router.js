import { createRouter, createWebHistory } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import Login from "@/pages/auth/Login.vue";
import Register from "@/pages/auth/Register.vue";
import ForgotPassword from "@/pages/auth/ForgotPassword.vue";
import ResetPassword from "@/pages/auth/ResetPassword.vue";

const routes = [
    {
        path: "/login",
        component: Login,
        name: "login",
    },
    {
        path: "/register",
        component: Register,
        name: "register",
    },
    {
        path: "/forgot-password",
        component: ForgotPassword,
        name: "forgot-password",
    },
    {
        path: "/reset-password/:token",
        component: ResetPassword,
        name: "reset-password",
    },

];

const router = createRouter({
     history: createWebHistory(import.meta.env.VITE_APP_BASE_URL),
    routes,
});


export default router;
