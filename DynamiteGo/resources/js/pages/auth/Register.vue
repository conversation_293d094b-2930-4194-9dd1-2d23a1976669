<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import LodengSpiner from "@/components/AllApp/LodengSpiner.vue";
import BigLogo from "@/components/AllApp/BigLogo.vue";
import InputForm from "@/components/FieldRequst/InputForm.vue";
import { useAuthStore } from "@/stores/auth";

const authStore = useAuthStore();
const form = ref({
    email: "",
    password: "",
    name: "",
    password_confirmation: "",
});
const loading = ref(false);
const router = useRouter();

async function sendData(data) {
    loading.value = true;
    const response = await authStore.handleRegister(data);
    form.value = { email: "", password: "",name:"",password_confirmation:"" };
    loading.value = response;
}

onMounted(() => {
    document.title = router.currentRoute.value.name || "Sign In";
});
</script>

<template>
    <template v-if="loading">
        <LodengSpiner />
    </template>

    <template v-else>
        <div
            class="flex flex-col justify-center px-4 py-8 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900"
        >
            <BigLogo>
                <template #default>
                    <h2
                        class="mt-6 text-center text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl"
                    >
                        Sign up to your account
                    </h2>
                </template>
            </BigLogo>
            <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md lg:max-w-lg">
                <div
                    class="bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-3xl sm:px-10"
                >
                    <form
                        class="space-y-6"
                        @submit.prevent="sendData(form)"
                        method="POST"
                    >
                    <InputForm
                            type="name"
                            name="name"
                            id="name"
                            autocomplete="name"
                            :required="true"
                            placeholder="name"
                            label="name"
                            v-model="form.name"
                            :errorMessage="authStore.errors.name || null"
                        />

                        <InputForm
                            type="email"
                            name="email"
                            id="email"
                            autocomplete="email"
                            :required="true"
                            placeholder="Email Address"
                            label="Email Address"
                            v-model="form.email"
                            :errorMessage="authStore.errors.email || null"
                        />
                         <InputForm
                            type="password"
                            name="password"
                            id="password"
                            autocomplete="password"
                            :required="true"
                            placeholder="Password"
                            label="Password"
                            v-model="form.password"
                            :errorMessage="authStore.errors.password || null"
                        />
<InputForm
                            type="password"
                            name="password_confirmation"
                            id="password_confirmation"
                            autocomplete="password_confirmation"
                            :required="true"
                            placeholder="password confirmation"
                            label="password confirmation"
                            v-model="form.password_confirmation"
                         />
                        <div>
                            <button
                                type="submit"
                                class="flex w-full justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-base"
                                :disabled="loading"
                            >
                                <span v-if="loading" class="loader"></span>
                                <span v-else>Sign up</span>
                            </button>
                        </div>
                    </form>
               
                    <p
                        class="mt-6 text-center text-sm text-gray-600 dark:text-gray-200"
                    >
                        Do you have an account?
                        <router-link
                            @click="authStore.clearErrors()"
                            :to="{ name: 'login' }"
                            class="font-medium text-indigo-600 hover:text-indigo-500"
                        >
                            Sign in
                        </router-link>
                    </p>
                </div>
            </div>
        </div>
    </template>
</template>
