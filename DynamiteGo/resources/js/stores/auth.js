import { defineStore } from "pinia";
import router from "@/router/router";

export const useAuthStore = defineStore("auth", {
    state: () => ({
        authUser: null,
        authErrors: [],
        authRole: null,
        authStatus: null,
    }),
    getters: {
        user: (state) => state.authUser,
        errors: (state) => state.authErrors,
        status: (state) => state.authStatus,
        roles: (state) => state.authRole,
    },
    actions: {
        async handleLogin(data) {
            try {
                const response = await axios.post("/api/login", data);
                this.authUser = response.data.user;
                this.authRole = response.data.user.roles;
                this.authStatus = response.data.status;
                this.authErrors = [];
                localStorage.setItem("token", response.data.token);
                router.push({ name: "home" });
                return true;
            } catch (error) {
                this.authErrors = error.response.data.errors;
                return false;
            }
        },
        async handleRegister(data) {
            try {
                const response = await axios.post("/api/register", data);
                this.authUser = response.data.user;
                this.authRole = response.data.user.roles;
                this.authStatus = response.data.status;
                this.authErrors = [];
                localStorage.setItem("token", response.data.token);
                router.push({ name: "home" });
                return true;
            } catch (error) {
                this.authErrors = error.response.data.errors;
                return false;
            }
        },
    },
});
