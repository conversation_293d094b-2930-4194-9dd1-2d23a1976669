import './bootstrap';
import { createApp, markRaw } from "vue";
import { createPinia } from "pinia";
import router from "@/router/router";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import { initFlowbite } from 'flowbite'
import App from "@/App.vue";

const app = createApp(App);
const pinia = createPinia(piniaPluginPersistedstate);

pinia.use(({ store }) => {
  store.router = markRaw(router);
});

app.use(pinia);
app.use(router);

initFlowbite();
app.mount("#app");
