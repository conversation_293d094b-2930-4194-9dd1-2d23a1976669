{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.1.13", "axios": "^1.11.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^2.0.0", "tailwindcss": "^4.1.13", "vite": "^7.0.4"}, "dependencies": {"@vitejs/plugin-vue": "^6.0.1", "flowbite": "^3.1.2", "flowbite-vue": "^0.2.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "vue-next": "^0.0.1", "vue-router": "^4.5.1"}}