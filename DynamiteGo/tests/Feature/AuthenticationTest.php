<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;
use Lara<PERSON>\Passport\Passport;
use Tests\TestCase;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create Personal Access Client for testing
        $this->artisan('passport:client', [
            '--personal' => true,
            '--name' => 'Test Personal Access Client',
            '--provider' => 'users'
        ]);
    }

    /**
     * Test user registration with Passport token generation.
     */
    public function test_user_can_register_and_receive_passport_token()
    {
        $userData = [
            'name' => $this->faker->name,
            'email' => $this->faker->unique()->safeEmail,
            'password' => 'password123',
        ];

        $response = $this->postJson('/api/register', $userData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'response_code',
                    'status',
                    'message',
                    'user_info' => [
                        'id',
                        'name',
                        'email',
                    ],
                    'token'
                ]);

        // Verify the token is a valid Passport token (not base64 encoded fallback)
        $token = $response->json('token');
        $this->assertNotEmpty($token);
        $this->assertIsString($token);

        // Passport tokens are typically longer than base64 encoded fallback tokens
        $this->assertGreaterThan(100, strlen($token));

        // Verify user was created in database
        $this->assertDatabaseHas('users', [
            'email' => $userData['email'],
            'name' => $userData['name'],
        ]);

        // Verify the token works for authenticated requests
        $userResponse = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/get-user');

        $userResponse->assertStatus(200);
    }

    /**
     * Test user login with Passport token generation.
     */
    public function test_user_can_login_and_receive_passport_token()
    {
        // Create a user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $response = $this->postJson('/api/login', $loginData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'response_code',
                    'status',
                    'message',
                    'user_info' => [
                        'id',
                        'name',
                        'email',
                    ],
                    'token'
                ]);

        // Verify the token is a valid Passport token
        $token = $response->json('token');
        $this->assertNotEmpty($token);
        $this->assertIsString($token);

        // Passport tokens are typically longer than base64 encoded fallback tokens
        $this->assertGreaterThan(100, strlen($token));

        // Verify the token works for authenticated requests
        $userResponse = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/get-user');

        $userResponse->assertStatus(200);
    }

    /**
     * Test login with invalid credentials.
     */
    public function test_login_with_invalid_credentials_fails()
    {
        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ];

        $response = $this->postJson('/api/login', $loginData);

        $response->assertStatus(401)
                ->assertJson([
                    'response_code' => 401,
                    'status' => 'error',
                    'message' => 'Unauthorized',
                ]);
    }

    /**
     * Test registration with invalid data fails.
     */
    public function test_registration_with_invalid_data_fails()
    {
        $invalidData = [
            'name' => '', // Empty name
            'email' => 'invalid-email', // Invalid email
            'password' => '123', // Too short password
        ];

        $response = $this->postJson('/api/register', $invalidData);

        $response->assertStatus(422); // Validation error
    }

    /**
     * Test that tokens can be used to access protected routes.
     */
    public function test_passport_token_allows_access_to_protected_routes()
    {
        $user = User::factory()->create();

        // Create a Passport token for the user
        $token = $user->createToken('test-token')->accessToken;

        // Test accessing a protected route
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/get-user');

        $response->assertStatus(200);
    }

    /**
     * Test logout functionality.
     */
    public function test_user_can_logout()
    {
        $user = User::factory()->create();
        $token = $user->createToken('test-token')->accessToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'response_code' => 200,
                    'status' => 'success',
                    'message' => 'Successfully logged out',
                ]);
    }
}
