<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\{Route,Auth,Log};
use App\Http\Controllers\api\v1\AuthenticationController;


Route::middleware('auth:api')->group(function () {
    Route::get('/user', function () {
        return Auth::user();
    });
});



Route::group([], function () {
    // --------------- Register and Login ----------------//

    Route::post('register', [AuthenticationController::class, 'register'])->name('register');
    Route::post('login', [AuthenticationController::class, 'login']);

    // ------------------ Get Data ----------------------//
    Route::middleware('auth:api')->group(function () {
        Route::get('get-user', [AuthenticationController::class, 'userInfo'])->name('get-user');
        Route::post('logout', [AuthenticationController::class, 'logOut'])->name('logout');
    });
});
