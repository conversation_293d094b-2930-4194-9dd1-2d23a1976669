<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المشروع - Project Details</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8fafc;
        }

        .nav-link {
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }

        .nav-link.active {
            border-color: #FF4500;
            color: #FF4500;
            font-weight: 700;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2rem;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #e74c3c;
            font-size: 1.2rem;
        }

        /* RTL/LTR Support */
        [dir="ltr"] .language-toggle {
            right: auto;
            left: 20px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
        }

        .visit-project-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 40px;
            border-radius: 30px;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .visit-project-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .loading {
            text-align: center;
            color: white;
            padding: 60px;
        }

        .loading-spinner {
            font-size: 3rem;
            animation: spin 2s linear infinite;
            margin-bottom: 20px;
        }

        .error {
            text-align: center;
            color: white;
            padding: 60px;
        }

        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                text-align: center;
            }

            .project-header {
                flex-direction: column;
                text-align: center;
            }

            .project-details {
                padding: 25px;
            }

            .project-info-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .controls {
                flex-direction: column;
                width: 100%;
            }

            .language-toggle, .back-button {
                width: 100%;
                justify-content: center;
            }
        }

        /* RTL/LTR specific styles */
        body.en .info-card {
            border-left: none;
            border-right: 5px solid #667eea;
        }

        body.en .features-list li {
            padding-right: 0;
            padding-left: 25px;
        }

        body.en .features-list li:before {
            right: auto;
            left: 0;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">
    <div class="container mx-auto p-4 sm:p-6 lg:p-8">

        <!-- Header with Language Toggle -->
        <header class="flex justify-between items-center mb-8 flex-wrap gap-4">
            <div>
                <h1 id="pageTitle" class="text-4xl font-bold text-slate-900 mb-2">تفاصيل المشروع</h1>
                <p id="pageSubtitle" class="text-lg text-slate-600">معلومات شاملة عن المشروع والتقنيات المستخدمة</p>
            </div>
            <div class="flex gap-4 items-center">
                <button onclick="toggleLanguage()" id="langToggle"
                        class="px-4 py-2 bg-white border-2 border-[#FF4500] text-[#FF4500] rounded-lg hover:bg-[#FF4500] hover:text-white transition-all duration-300">
                    English
                </button>
                <a href="./index.html"
                   class="px-4 py-2 bg-[#FF4500] text-white rounded-lg hover:bg-[#FF6500] transition-all duration-300 flex items-center gap-2">
                    <span id="backText">العودة للرئيسية</span>
                    <span>🏠</span>
                </a>
            </div>
        </header>

        <!-- Loading Section -->
        <div id="loadingSection" class="loading">
            <div class="text-center">
                <div class="text-6xl mb-4">⚙️</div>
                <h3 id="loadingText" class="text-xl">جاري تحميل تفاصيل المشروع...</h3>
            </div>
        </div>

        <!-- Error Section -->
        <div id="errorSection" class="error" style="display: none;">
            <div class="text-center">
                <div class="text-6xl mb-4">⚠️</div>
                <h3 id="errorTitle" class="text-xl mb-2">خطأ في تحميل المشروع</h3>
                <p id="errorMessage"></p>
            </div>
        </div>

        <!-- Project Content -->
        <main id="projectContent" style="display: none;">
            <!-- Project Overview Section -->
            <section id="overview" class="tab-content active">
                <div class="bg-white p-6 rounded-lg shadow-md mb-8">
                    <div class="flex items-center gap-6 mb-6 pb-6 border-b border-slate-200">
                        <div id="projectIcon" class="text-6xl bg-slate-100 p-4 rounded-lg">🚀</div>
                        <div class="flex-1">
                            <h1 id="projectTitle" class="text-3xl font-bold text-slate-900 mb-2"></h1>
                            <div id="projectStatus" class="inline-block px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-4"></div>
                            <p id="projectDescription" class="text-slate-700 text-lg leading-relaxed"></p>
                        </div>
                    </div>
                </div>

                <!-- Project Details Grid -->
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <!-- Technologies Card -->
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-4 text-[#FF4500] flex items-center gap-2">
                            <span>⚙️</span>
                            <span id="techTitle">التقنيات المستخدمة</span>
                        </h3>
                        <div id="technologiesList" class="flex flex-wrap gap-2"></div>
                    </div>

                    <!-- Project Info Card -->
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h3 class="text-xl font-bold mb-4 text-[#FF4500] flex items-center gap-2">
                            <span>📅</span>
                            <span id="infoTitle">معلومات المشروع</span>
                        </h3>
                        <div class="space-y-3">
                            <div>
                                <span id="startDateLabel" class="font-semibold text-slate-700">تاريخ البدء:</span>
                                <span id="startDate" class="text-slate-600"></span>
                            </div>
                            <div>
                                <span id="lastUpdatedLabel" class="font-semibold text-slate-700">آخر تحديث:</span>
                                <span id="lastUpdated" class="text-slate-600"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Features Card -->
                    <div class="bg-white p-6 rounded-lg shadow-md md:col-span-2 lg:col-span-1">
                        <h3 class="text-xl font-bold mb-4 text-[#FF4500] flex items-center gap-2">
                            <span>✨</span>
                            <span id="featuresTitle">المميزات الرئيسية</span>
                        </h3>
                        <ul id="featuresList" class="space-y-2"></ul>
                    </div>
                </div>

                <!-- Detailed Description -->
                <div class="bg-white p-6 rounded-lg shadow-md mb-8">
                    <h2 class="text-2xl font-bold mb-4 text-[#FF4500] flex items-center gap-2">
                        <span>📋</span>
                        <span id="detailsTitle">الوصف التفصيلي</span>
                    </h2>
                    <p id="detailedDescription" class="text-slate-700 leading-relaxed text-lg"></p>
                </div>

                <!-- Project Actions -->
                <div class="text-center">
                    <a id="visitProjectBtn" href="#" target="_blank"
                       class="inline-flex items-center gap-3 px-8 py-4 bg-[#FF4500] text-white rounded-lg hover:bg-[#FF6500] transition-all duration-300 text-lg font-semibold">
                        <span id="visitProjectText">زيارة المشروع</span>
                        <span>🚀</span>
                    </a>
                </div>
            </section>
        </main>
    </div>

    <script src="./projects-data.js"></script>
    <script>
        let currentLanguage = localStorage.getItem('preferredLanguage') || 'ar';
        let projectData = null;

        // تطبيق اللغة المحفوظة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            applyLanguage();
            loadProjectDetails();
        });

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            localStorage.setItem('preferredLanguage', currentLanguage);
            applyLanguage();
            if (projectData) {
                renderProjectDetails(projectData);
            }
        }

        function applyLanguage() {
            const body = document.body;
            const html = document.documentElement;

            if (currentLanguage === 'en') {
                body.classList.add('en');
                html.setAttribute('lang', 'en');
                html.setAttribute('dir', 'ltr');

                document.getElementById('pageTitle').textContent = 'Project Details';
                document.getElementById('pageSubtitle').textContent = 'Comprehensive information about the project and technologies used';
                document.getElementById('langToggle').textContent = 'العربية';
                document.getElementById('backText').textContent = 'Back to Home';
                document.getElementById('loadingText').textContent = 'Loading project details...';
                document.getElementById('errorTitle').textContent = 'Error loading project';

                // Update section titles if they exist
                const techTitle = document.getElementById('techTitle');
                const infoTitle = document.getElementById('infoTitle');
                const featuresTitle = document.getElementById('featuresTitle');
                const detailsTitle = document.getElementById('detailsTitle');
                const startDateLabel = document.getElementById('startDateLabel');
                const lastUpdatedLabel = document.getElementById('lastUpdatedLabel');
                const visitProjectText = document.getElementById('visitProjectText');

                if (techTitle) techTitle.textContent = 'Technologies Used';
                if (infoTitle) infoTitle.textContent = 'Project Information';
                if (featuresTitle) featuresTitle.textContent = 'Key Features';
                if (detailsTitle) detailsTitle.textContent = 'Detailed Description';
                if (startDateLabel) startDateLabel.textContent = 'Start Date:';
                if (lastUpdatedLabel) lastUpdatedLabel.textContent = 'Last Updated:';
                if (visitProjectText) visitProjectText.textContent = 'Visit Project';
            } else {
                body.classList.remove('en');
                html.setAttribute('lang', 'ar');
                html.setAttribute('dir', 'rtl');

                document.getElementById('pageTitle').textContent = 'تفاصيل المشروع';
                document.getElementById('pageSubtitle').textContent = 'معلومات شاملة عن المشروع والتقنيات المستخدمة';
                document.getElementById('langToggle').textContent = 'English';
                document.getElementById('backText').textContent = 'العودة للرئيسية';
                document.getElementById('loadingText').textContent = 'جاري تحميل تفاصيل المشروع...';
                document.getElementById('errorTitle').textContent = 'خطأ في تحميل المشروع';

                // Update section titles if they exist
                const techTitle = document.getElementById('techTitle');
                const infoTitle = document.getElementById('infoTitle');
                const featuresTitle = document.getElementById('featuresTitle');
                const detailsTitle = document.getElementById('detailsTitle');
                const startDateLabel = document.getElementById('startDateLabel');
                const lastUpdatedLabel = document.getElementById('lastUpdatedLabel');
                const visitProjectText = document.getElementById('visitProjectText');

                if (techTitle) techTitle.textContent = 'التقنيات المستخدمة';
                if (infoTitle) infoTitle.textContent = 'معلومات المشروع';
                if (featuresTitle) featuresTitle.textContent = 'المميزات الرئيسية';
                if (detailsTitle) detailsTitle.textContent = 'الوصف التفصيلي';
                if (startDateLabel) startDateLabel.textContent = 'تاريخ البدء:';
                if (lastUpdatedLabel) lastUpdatedLabel.textContent = 'آخر تحديث:';
                if (visitProjectText) visitProjectText.textContent = 'زيارة المشروع';
            }
        }

        async function loadProjectDetails() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const projectId = urlParams.get('id');

                console.log('Loading project with ID:', projectId); // للتشخيص

                if (!projectId) {
                    throw new Error(currentLanguage === 'ar' ? 'معرف المشروع مفقود' : 'Project ID is missing');
                }

                let data;

                // محاولة استخدام البيانات المحملة مسبقاً
                if (window.projectsData) {
                    console.log('Using embedded data'); // للتشخيص
                    data = window.projectsData;
                } else {
                    // محاولة تحميل البيانات من JSON
                    console.log('Fetching data from JSON'); // للتشخيص
                    const response = await fetch('./projects.json?v=2');
                    console.log('Response status:', response.status); // للتشخيص

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    data = await response.json();
                }

                console.log('Loaded data:', data); // للتشخيص

                if (!data || !data.success) {
                    throw new Error(data?.error || (currentLanguage === 'ar' ? 'خطأ غير معروف' : 'Unknown error'));
                }

                if (!data.projects || !Array.isArray(data.projects)) {
                    throw new Error(currentLanguage === 'ar' ? 'بيانات المشاريع غير صحيحة' : 'Invalid projects data');
                }

                console.log('Looking for project ID:', projectId); // للتشخيص
                console.log('Available projects:', data.projects.map(p => ({ id: p?.id, title: p?.title }))); // للتشخيص
                console.log('First project:', data.projects[0]); // للتشخيص

                const project = data.projects.find(p => {
                    console.log('Checking project:', p.id, 'against:', projectId);
                    return p && p.id && p.id === projectId;
                });

                if (!project) {
                    // محاولة البحث بالعنوان كبديل
                    const projectByTitle = data.projects.find(p => {
                        const title = typeof p.title === 'object' ? p.title.ar : p.title;
                        return title && title.toLowerCase().replace(/\s+/g, '') === projectId;
                    });

                    if (projectByTitle) {
                        projectData = projectByTitle;
                        renderProjectDetails(projectByTitle);
                        return;
                    }

                    throw new Error(currentLanguage === 'ar' ? `المشروع غير موجود: ${projectId}` : `Project not found: ${projectId}`);
                }

                projectData = project;
                renderProjectDetails(project);

            } catch (error) {
                console.error('Error loading project details:', error);
                showError(error.message);
            }
        }

        function renderProjectDetails(project) {
            const lang = currentLanguage;

            // Update project icon
            document.getElementById('projectIcon').textContent = project.icon;

            // Update project title
            document.getElementById('projectTitle').textContent = project.title[lang];

            // Update project status
            const statusElement = document.getElementById('projectStatus');
            const statusText = project.status === 'active' ?
                (lang === 'ar' ? 'نشط' : 'Active') :
                (lang === 'ar' ? 'قيد التطوير' : 'In Development');
            statusElement.textContent = statusText;
            statusElement.className = project.status === 'active' ?
                'inline-block px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 mb-4' :
                'inline-block px-4 py-2 rounded-full text-sm font-medium bg-orange-100 text-orange-800 mb-4';

            // Update project description
            document.getElementById('projectDescription').textContent = project.description[lang];

            // Update technologies list
            const techList = document.getElementById('technologiesList');
            techList.innerHTML = project.technologies.map(tech =>
                `<span class="px-3 py-1 bg-slate-100 text-slate-700 rounded-full text-sm font-medium">${tech}</span>`
            ).join('');

            // Update project dates
            document.getElementById('startDate').textContent = formatDate(project.startDate, lang);
            document.getElementById('lastUpdated').textContent = formatDate(project.lastUpdated, lang);

            // Update features list
            const featuresList = document.getElementById('featuresList');
            featuresList.innerHTML = project.features[lang].map(feature =>
                `<li class="flex items-start gap-2">
                    <span class="text-[#FF4500] font-bold mt-1">✓</span>
                    <span class="text-slate-700">${feature}</span>
                </li>`
            ).join('');

            // Update detailed description
            document.getElementById('detailedDescription').textContent = project.detailedDescription[lang];

            // Update visit project button
            const visitBtn = document.getElementById('visitProjectBtn');
            visitBtn.href = project.url;

            // Apply language-specific labels
            applyLanguage();

            // Show content and hide loading
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('errorSection').style.display = 'none';
            document.getElementById('projectContent').style.display = 'block';

            // Add fade-in animation
            const content = document.getElementById('projectContent');
            content.style.opacity = '0';
            content.style.transform = 'translateY(30px)';

            setTimeout(() => {
                content.style.transition = 'all 0.6s ease';
                content.style.opacity = '1';
                content.style.transform = 'translateY(0)';
            }, 100);
        }

        function showError(message) {
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('projectContent').style.display = 'none';

            const errorSection = document.getElementById('errorSection');
            document.getElementById('errorMessage').textContent = message;
            errorSection.style.display = 'block';
        }

        function formatDate(dateString, lang) {
            const date = new Date(dateString);
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };

            if (lang === 'ar') {
                return date.toLocaleDateString('ar-SA', options);
            } else {
                return date.toLocaleDateString('en-US', options);
            }
        }
    </script>
</body>
</html>
