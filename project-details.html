<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المشروع - Project Details</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            transition: all 0.3s ease;
        }

        body.en {
            direction: ltr;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            color: white;
            flex-wrap: wrap;
            gap: 20px;
        }

        .header-content h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-content p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .language-toggle {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
        }

        .language-toggle:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .back-button {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .project-details {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }

        .project-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .project-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            flex-shrink: 0;
        }

        .project-title-section h1 {
            font-size: 2.2rem;
            color: #333;
            margin-bottom: 10px;
        }

        .project-status {
            display: inline-block;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            background: #e8f5e8;
            color: #2d5a2d;
        }

        .project-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .info-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-card p, .info-card ul {
            color: #666;
            line-height: 1.6;
        }

        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .tech-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .features-list {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-right: 25px;
        }

        .features-list li:before {
            content: '✓';
            position: absolute;
            right: 0;
            color: #667eea;
            font-weight: bold;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .description-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .description-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .description-section p {
            color: #666;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .project-actions {
            text-align: center;
            margin-top: 40px;
        }

        .visit-project-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 40px;
            border-radius: 30px;
            text-decoration: none;
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .visit-project-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .loading {
            text-align: center;
            color: white;
            padding: 60px;
        }

        .loading-spinner {
            font-size: 3rem;
            animation: spin 2s linear infinite;
            margin-bottom: 20px;
        }

        .error {
            text-align: center;
            color: white;
            padding: 60px;
        }

        .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                text-align: center;
            }

            .project-header {
                flex-direction: column;
                text-align: center;
            }

            .project-details {
                padding: 25px;
            }

            .project-info-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .controls {
                flex-direction: column;
                width: 100%;
            }

            .language-toggle, .back-button {
                width: 100%;
                justify-content: center;
            }
        }

        /* RTL/LTR specific styles */
        body.en .info-card {
            border-left: none;
            border-right: 5px solid #667eea;
        }

        body.en .features-list li {
            padding-right: 0;
            padding-left: 25px;
        }

        body.en .features-list li:before {
            right: auto;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1 id="pageTitle">تفاصيل المشروع</h1>
                <p id="pageSubtitle">معلومات شاملة عن المشروع والتقنيات المستخدمة</p>
            </div>
            <div class="controls">
                <button class="language-toggle" onclick="toggleLanguage()" id="langToggle">English</button>
                <a href="/" class="back-button">
                    <span id="backText">العودة للرئيسية</span>
                    <span>🏠</span>
                </a>
            </div>
        </div>

        <div id="loadingSection" class="loading">
            <div class="loading-spinner">⚙️</div>
            <h3 id="loadingText">جاري تحميل تفاصيل المشروع...</h3>
        </div>

        <div id="errorSection" class="error" style="display: none;">
            <div class="error-icon">⚠️</div>
            <h3 id="errorTitle">خطأ في تحميل المشروع</h3>
            <p id="errorMessage"></p>
        </div>

        <div id="projectContent" style="display: none;">
            <!-- سيتم تحميل محتوى المشروع هنا -->
        </div>
    </div>

    <script>
        let currentLanguage = localStorage.getItem('preferredLanguage') || 'ar';
        let projectData = null;

        // تطبيق اللغة المحفوظة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            applyLanguage();
            loadProjectDetails();
        });

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            localStorage.setItem('preferredLanguage', currentLanguage);
            applyLanguage();
            if (projectData) {
                renderProjectDetails(projectData);
            }
        }

        function applyLanguage() {
            const body = document.body;
            const html = document.documentElement;

            if (currentLanguage === 'en') {
                body.classList.add('en');
                html.setAttribute('lang', 'en');
                html.setAttribute('dir', 'ltr');

                document.getElementById('pageTitle').textContent = 'Project Details';
                document.getElementById('pageSubtitle').textContent = 'Comprehensive information about the project and technologies used';
                document.getElementById('langToggle').textContent = 'العربية';
                document.getElementById('backText').textContent = 'Back to Home';
                document.getElementById('loadingText').textContent = 'Loading project details...';
                document.getElementById('errorTitle').textContent = 'Error loading project';
            } else {
                body.classList.remove('en');
                html.setAttribute('lang', 'ar');
                html.setAttribute('dir', 'rtl');

                document.getElementById('pageTitle').textContent = 'تفاصيل المشروع';
                document.getElementById('pageSubtitle').textContent = 'معلومات شاملة عن المشروع والتقنيات المستخدمة';
                document.getElementById('langToggle').textContent = 'English';
                document.getElementById('backText').textContent = 'العودة للرئيسية';
                document.getElementById('loadingText').textContent = 'جاري تحميل تفاصيل المشروع...';
                document.getElementById('errorTitle').textContent = 'خطأ في تحميل المشروع';
            }
        }

        async function loadProjectDetails() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const projectId = urlParams.get('id');

                if (!projectId) {
                    throw new Error(currentLanguage === 'ar' ? 'معرف المشروع مفقود' : 'Project ID is missing');
                }

                const response = await fetch('/projects.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (!data.success) {
                    throw new Error(data.error || (currentLanguage === 'ar' ? 'خطأ غير معروف' : 'Unknown error'));
                }

                const project = data.projects.find(p => p.id === projectId);
                if (!project) {
                    throw new Error(currentLanguage === 'ar' ? 'المشروع غير موجود' : 'Project not found');
                }

                projectData = project;
                renderProjectDetails(project);

            } catch (error) {
                console.error('Error loading project details:', error);
                showError(error.message);
            }
        }

        function renderProjectDetails(project) {
            const lang = currentLanguage;
            const content = document.getElementById('projectContent');

            content.innerHTML = `
                <div class="project-details">
                    <div class="project-header">
                        <div class="project-icon">${project.icon}</div>
                        <div class="project-title-section">
                            <h1>${project.title[lang]}</h1>
                            <span class="project-status">
                                ${project.status === 'active' ? (lang === 'ar' ? 'نشط' : 'Active') : (lang === 'ar' ? 'قيد التطوير' : 'In Development')}
                            </span>
                        </div>
                    </div>

                    <div class="description-section">
                        <h2>
                            <span>📋</span>
                            ${lang === 'ar' ? 'وصف المشروع' : 'Project Description'}
                        </h2>
                        <p>${project.detailedDescription[lang]}</p>
                    </div>

                    <div class="project-info-grid">
                        <div class="info-card">
                            <h3>
                                <span>🛠️</span>
                                ${lang === 'ar' ? 'التقنيات المستخدمة' : 'Technologies Used'}
                            </h3>
                            <div class="tech-tags">
                                ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                            </div>
                        </div>

                        <div class="info-card">
                            <h3>
                                <span>📅</span>
                                ${lang === 'ar' ? 'معلومات التواريخ' : 'Date Information'}
                            </h3>
                            <p><strong>${lang === 'ar' ? 'تاريخ البدء:' : 'Start Date:'}</strong> ${formatDate(project.startDate, lang)}</p>
                            <p><strong>${lang === 'ar' ? 'آخر تحديث:' : 'Last Updated:'}</strong> ${formatDate(project.lastUpdated, lang)}</p>
                        </div>

                        <div class="info-card">
                            <h3>
                                <span>⭐</span>
                                ${lang === 'ar' ? 'المميزات الرئيسية' : 'Key Features'}
                            </h3>
                            <ul class="features-list">
                                ${project.features[lang].map(feature => `<li>${feature}</li>`).join('')}
                            </ul>
                        </div>

                        <div class="info-card">
                            <h3>
                                <span>📊</span>
                                ${lang === 'ar' ? 'حالة المشروع' : 'Project Status'}
                            </h3>
                            <p><strong>${lang === 'ar' ? 'الحالة:' : 'Status:'}</strong> ${project.status === 'active' ? (lang === 'ar' ? 'نشط ومتاح' : 'Active and Available') : (lang === 'ar' ? 'قيد التطوير' : 'In Development')}</p>
                            <p><strong>${lang === 'ar' ? 'عدد التقنيات:' : 'Technologies Count:'}</strong> ${project.technologies.length}</p>
                            <p><strong>${lang === 'ar' ? 'عدد المميزات:' : 'Features Count:'}</strong> ${project.features[lang].length}</p>
                        </div>
                    </div>

                    <div class="project-actions">
                        <a href="${project.url}" class="visit-project-btn">
                            <span>${lang === 'ar' ? 'زيارة المشروع' : 'Visit Project'}</span>
                            <span>🚀</span>
                        </a>
                    </div>
                </div>
            `;

            // إخفاء شاشة التحميل وإظهار المحتوى
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('errorSection').style.display = 'none';
            content.style.display = 'block';

            // إضافة تأثير الظهور
            content.style.opacity = '0';
            content.style.transform = 'translateY(30px)';

            setTimeout(() => {
                content.style.transition = 'all 0.6s ease';
                content.style.opacity = '1';
                content.style.transform = 'translateY(0)';
            }, 100);
        }

        function showError(message) {
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('projectContent').style.display = 'none';

            const errorSection = document.getElementById('errorSection');
            document.getElementById('errorMessage').textContent = message;
            errorSection.style.display = 'block';
        }

        function formatDate(dateString, lang) {
            const date = new Date(dateString);
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };

            if (lang === 'ar') {
                return date.toLocaleDateString('ar-SA', options);
            } else {
                return date.toLocaleDateString('en-US', options);
            }
        }
    </script>
</body>
</html>
