// Static projects data - يمكن تحديثها يدوياً أو تحويلها لـ API لاحقاً
const projectsData = {
    success: true,
    projects: [
        {
            title: "PsychAI",
            description: "مشروع Laravel للذكاء الاصطناعي النفسي - نظام تحليل نفسي متقدم",
            url: "/PsychAI/",
            icon: "🧠",
            status: "active"
        } 
    ],
    total: 3
};

// إذا تم استدعاء الملف مباشرة، أرجع البيانات
if (typeof window !== 'undefined') {
    // في المتصفح
    window.projectsData = projectsData;
} else if (typeof module !== 'undefined' && module.exports) {
    // في Node.js
    module.exports = projectsData;
}
